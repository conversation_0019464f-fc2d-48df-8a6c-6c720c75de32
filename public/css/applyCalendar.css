/* 全局设置 */
* {
  box-sizing: border-box;
}

body {
  margin: 0;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'PingFang SC',
    'Helvetica Neue', 'Hiragino Sans GB', 'Microsoft YaHei', Arial, sans-serif;
  background-color: #fff;
}

.container {
  width: 100%;
  max-width: 677px;
  margin: 0 auto;
  padding: 20px 0 50px 0;
  position: relative;
}

.w100 {
  width: 100%;
}

.container img {
  max-width: 100%;
  height: auto;
  display: block;
}

.container a {
  color: #336699;
  text-decoration: underline;
}

.title {
  font-size: 22px;
  line-height: 1.4;
  margin-top: 0;
  margin-bottom: 14px;
  font-weight: 500;
}

.meta {
  margin-bottom: 10px;
  font-size: 15px;
  color: #576b95;
}

.meta .author,
.meta .date {
  display: inline-block;
  vertical-align: middle;
  margin-right: 10px;
}

.meta .date {
  color: rgba(0, 0, 0, 0.3);
}

.content {
  font-size: 15px;
  color: #444;
  line-height: 1.8;
}

.content img {
  max-width: 100%;
  height: auto;
  display: block;
  margin: 10px auto;
}

.content a.rsvp-button-blue {
  display: inline-block;
  padding: 0 15px;
  height: 44px;
  background: #2875e8;
  border-radius: 4px;
  color: #fff;
  font-weight: bold;
  text-align: center;
  line-height: 44px;
  text-decoration: none;
  transition: background-color 0.15s ease-in-out;
}

.content a.rsvp-button-blue:hover {
  background-color: #5090ff;
}

.content blockquote {
  color: #6a737d;
  border-left: 4px solid #eaecef;
  padding-left: 1em;
  margin: 1em 0;
}

.qr_code_pc {
  position: fixed;
  left: 50%;
  transform: translateX(338.5px);
  top: 30px;
  width: 140px;
  padding: 16px;
  margin-left: 20px;
  border: 1px solid #d9dadc;
  background-color: #fff;
  word-wrap: break-word;
  hyphens: auto;
  box-sizing: border-box;
  text-align: center;
}

.qr_code_pc p {
  font-size: 14px;
  line-height: 20px;
  color: #717375;
  margin-top: 5px;
  margin-bottom: 5px;
}

@media (max-width: 768px) {
  .container {
    padding: 20px 15px 50px 15px;
  }

  .title {
    font-size: 22px;
  }

  .content {
    font-size: 15px;
  }

  .meta {
    font-size: 15px;
  }

  .content a.rsvp-button-blue {
    height: 48px;
    line-height: 48px;
  }

  .qr_code_pc {
    display: none;
  }
}
