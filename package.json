{"name": "connect-v7.1", "version": "7.1.0", "description": "一个项目用COOL就够了", "private": true, "dependencies": {"@alicloud/green20220302": "^2.20.4", "@alicloud/pop-core": "^1.8.0", "@alicloud/tingwu20220930": "^1.0.0", "@cool-midway/core": "^7.1.18", "@cool-midway/es": "^7.0.0", "@cool-midway/file": "^7.0.5", "@cool-midway/rpc": "^7.0.0", "@cool-midway/task": "^7.0.0", "@langchain/anthropic": "^0.2.2", "@langchain/openai": "^0.2.1", "@midwayjs/bootstrap": "^3.15.8", "@midwayjs/bull": "^3.16.8", "@midwayjs/cache-manager": "^3.15.8", "@midwayjs/core": "^3.15.8", "@midwayjs/cron": "^3.15.9", "@midwayjs/cross-domain": "^3.15.10", "@midwayjs/decorator": "^3.15.8", "@midwayjs/info": "^3.15.10", "@midwayjs/koa": "^3.15.10", "@midwayjs/logger": "^3.4.0", "@midwayjs/oss": "^3.12.3", "@midwayjs/redis": "^3.15.8", "@midwayjs/socketio": "^3.2.0", "@midwayjs/static-file": "^3.15.10", "@midwayjs/typeorm": "^3.15.8", "@midwayjs/upload": "^3.15.10", "@midwayjs/validate": "^3.15.10", "@midwayjs/view-ejs": "^3.15.10", "@wecom/crypto": "^1.0.1", "ali-oss": "^6.23.0", "authcode": "^0.0.1", "axios": "^1.6.8", "cache-manager-ioredis-yet": "^2.0.3", "cfork": "^1.10.0", "cheerio": "^1.0.0", "decompress": "^4.2.1", "download": "^8.0.0", "file-box": "^1.5.5", "fs-extra": "^10.1.0", "html-entities": "^2.5.2", "image-size": "^1.1.1", "ipip-ipdb": "^0.6.0", "jsonwebtoken": "^9.0.2", "langchain": "^0.2.8", "lodash": "^4.17.21", "md5": "^2.3.0", "mime-types": "^2.1.35", "mini-svg-data-uri": "^1.4.4", "moment": "^2.30.1", "moment-timezone": "^0.5.34", "mssql": "^10.0.2", "mysql2": "^3.9.7", "nanoid": "^3.3.7", "node-xlsx": "^0.21.0", "nodemailer": "^6.9.6", "openai": "^4.20.0", "phpunserialize": "^1.1.0", "puppeteer": "^19.7.2", "qrcode": "^1.5.3", "qrcode-terminal": "^0.12.0", "randomstring": "^1.2.2", "rss-parser": "^3.13.0", "sharp": "^0.32.6", "svg-captcha": "^1.4.0", "svg2png-wasm": "^1.4.1", "typeorm": "^0.3.20", "useragent": "^2.3.0", "uuid": "^9.0.1", "wechaty": "^1.20.2", "wechaty-puppet-padlocal": "^1.20.1", "ws": "^8.16.0"}, "devDependencies": {"@midwayjs/cli": "^2.1.1", "@midwayjs/mock": "^3.15.8", "@types/jest": "^29.5.12", "@types/koa": "^2.15.0", "@types/node": "20", "cross-env": "^7.0.3", "jest": "^29.7.0", "mwts": "^1.3.0", "mwtsc": "^1.7.2", "ts-jest": "^29.1.2", "typescript": "~5.4.5"}, "engines": {"node": ">=12.0.0"}, "scripts": {"start": "NODE_ENV=production node ./server.js", "dev": "cross-env && cross-env NODE_ENV=local TS_NODE_TYPE_CHECK=false TS_NODE_TRANSPILE_ONLY=true midway-bin dev --ts", "cov": "midway-bin cov --ts", "lint": "mwts check", "lint:fix": "mwts fix", "ci": "npm run cov", "build": "midway-bin build -c && node ./copyFile.js"}, "midway-bin-clean": [".vscode/.tsbuildinfo", "dist"]}