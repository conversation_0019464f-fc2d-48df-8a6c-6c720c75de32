import { Inject, Provide, Config, Logger } from '@midwayjs/decorator';
import { CoolCommException } from '@cool-midway/core';
import { Context } from '@midwayjs/koa';
import { createWriteStream } from 'fs';
import axios from 'axios';
import * as ipdb from 'ipip-ipdb';
import * as _ from 'lodash';
import * as md5 from 'md5';
import * as authcode from 'authcode';
import * as randomString from 'randomstring';
import * as fse from 'fs-extra';
import * as fs from 'node:fs/promises';
const { URL } = require('url');
import { ILogger } from '@midwayjs/logger';
import { HumanMessage, AIMessage } from '@langchain/core/messages';
const mime = require('mime-types');
const util = require('util');
const sharp = require('sharp');
const exec = util.promisify(require('child_process').exec);
const moment = require('moment-timezone');
const { sign } = require('../comm/X-Bogus.js');
const { generate_a_bogus } = require('../comm/a_bogus.js');
import { customAlphabet } from 'nanoid';

const nanoid = customAlphabet(
  '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ',
  10
);
sharp.cache(false);

/**
 * 帮助类
 */
@Provide()
export class Utils {
  @Inject()
  ctx: Context;

  @Logger()
  logger: ILogger;

  @Config('discuz')
  discuz;

  @Config('douyin')
  douyin;

  @Config('custom')
  custom;

  @Inject()
  baseDir;

  /**
   * 判断是否管理员
   */
  async isAdmin(ctx: Context) {
    const admins = ['1', '10'];
    const roleIds = ctx.admin.roleIds;

    for (let i = 0; i < roleIds.length; i++) {
      if (admins.includes(roleIds[i])) {
        return true;
      }
    }
    return false;
  }

  /**
   * 获得请求IP
   */
  async getReqIP(ctx: Context) {
    const req = ctx.req;
    return (
      req?.headers['x-forwarded-for'] ||
      req?.socket?.remoteAddress?.replace('::ffff:', '') ||
      '127.0.0.1'
    );
  }

  /**
   * 根据IP获得请求地址
   * @param ip 为空时则为当前请求的IP地址
   */
  async getIpAddr(ctx: Context, ip?: string | string[]) {
    try {
      if (!ip) {
        ip = await this.getReqIP(ctx);
      }
      const bst = new ipdb.BaseStation(`${this.baseDir}/comm/ipipfree.ipdb`);
      const result = bst.findInfo(ip, 'CN');
      const addArr: any = [];
      if (result) {
        addArr.push(result.countryName);
        addArr.push(result.regionName);
        addArr.push(result.cityName);
        return _.uniq(addArr).join('');
      }
    } catch (err) {
      return '无法获取地址信息';
    }
  }

  /**
   * 去除对象的空值属性
   * @param obj
   */
  async removeEmptyP(obj) {
    Object.keys(obj).forEach(key => {
      if (obj[key] === null || obj[key] === '' || obj[key] === 'undefined') {
        delete obj[key];
      }
    });
  }

  /**
   * 线程阻塞毫秒数
   * @param ms
   */
  sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * 随机1到sec秒
   * @param sec
   */
  async randomSleep(sec: number) {
    const randomTime = Math.floor(Math.random() * sec + 1) * 1000;
    await this.sleep(randomTime);
  }

  async checkForumStatus(ctx: Context) {
    ctx.cookies.get('foo', { encrypt: true });
  }

  encodeDZString(str: string) {
    return encodeURIComponent(
      authcode(str, 'ENCODE', md5(this.discuz.authkey))
    );
  }

  decodeDZString(str: string) {
    return authcode(
      decodeURIComponent(str),
      'DECODE',
      md5(`${this.discuz.authkey}`)
    );
  }

  encodeDZAuthkey(
    ctx: Context,
    str: string,
    saltkey: string,
    writeCookie = true,
    autoLogin = true
  ) {
    const maxAge = autoLogin ? this.discuz.cookieMaxAge : 0;

    const cookie = {
      saltkey: saltkey,
      auth: encodeURIComponent(
        authcode(str, 'ENCODE', md5(this.discuz.authkey + saltkey))
      ),
    };

    if (writeCookie) {
      const domain = this.discuz.domain;
      ctx.cookies.set(`${this.discuz.prefix}auth`, cookie.auth, {
        httpOnly: true,
        maxAge: maxAge,
        domain,
      });
      ctx.cookies.set(`${this.discuz.prefix}saltkey`, cookie.saltkey, {
        httpOnly: false,
        maxAge: maxAge,
        domain,
      });
    }

    return cookie;
  }

  decodeDZAuthkey(ctx: Context) {
    const auth = ctx.cookies.get(`${this.discuz.prefix}auth`, {
      signed: false,
    });

    const saltkey = ctx.cookies.get(`${this.discuz.prefix}saltkey`, {
      signed: false,
    });

    if (!auth || !saltkey) return '';

    return authcode(
      decodeURIComponent(auth),
      'DECODE',
      md5(`${this.discuz.authkey}${saltkey}`)
    );
  }

  decodeLocalAuthkey(auth: string, saltkey: string) {
    return authcode(
      decodeURIComponent(auth),
      'DECODE',
      md5(`${this.discuz.authkey}${saltkey}`)
    );
  }

  getUserinfoFromCookie(ctx: Context, checkNewSystem = true) {
    let userinfo = this.decodeDZAuthkey(ctx);
    if (!userinfo) throw new CoolCommException('请先登录系统');

    userinfo = userinfo.split('\t');

    if (checkNewSystem && userinfo.length < 3) {
      throw new CoolCommException('请先登录新系统');
    }

    return userinfo;
  }

  stringUTF8Len(str: string) {
    return Buffer.byteLength(str, 'utf8');
  }

  randomstring(len) {
    return randomString.generate(len);
  }

  isMidnight() {
    const currentTime = moment().tz('Asia/Shanghai');
    return currentTime.hour() >= 0 && currentTime.hour() <= 7;
  }

  afterNow99Years() {
    return moment().tz('Asia/Shanghai').unix() + 60 * 60 * 24 * 365 * 50;
  }

  now() {
    return moment().tz('Asia/Shanghai').unix();
  }

  today(format = 'YYYY-MM-DD HH:mm:ss') {
    return moment().tz('Asia/Shanghai').format(format);
  }

  yesterday(format = 'YYYY-MM-DD HH:mm:ss') {
    return moment().tz('Asia/Shanghai').subtract(1, 'days').format(format);
  }

  futureTimestamp(days) {    
    const now = moment().tz('Asia/Shanghai');
    return now.add(days, 'days').startOf('day').unix();
  }

  todayTS() {
    return moment().startOf('day').unix();
  }

  subDaysTS(days) {
    return moment().subtract(days, 'days').unix();
  }

  week(startFormat = 'YYYY-MM-DD', endFormat = 'YYYY-MM-DD') {
    const now = moment().tz('Asia/Shanghai');

    const startOfWeek = now
      .clone()
      .locale('zh-cn', {
        week: {
          dow: 1,
        },
      })
      .startOf('week')
      .format(startFormat);

    const endOfWeek = now
      .clone()
      .locale('zh-cn', {
        week: {
          dow: 1,
        },
      })
      .endOf('week')
      .format(endFormat);

    return `${startOfWeek}-${endOfWeek}`;
  }

  dateToTimeStamp(date) {
    return moment(date).tz('Asia/Shanghai').unix();
  }

  timeStampToDate(ticks, format = 'YYYY-MM-DD HH:mm:ss') {
    const timestamp = (ticks - 621355968000000000) / 10000000;
    return moment.unix(timestamp).format(format);
  }

  timestampToDate(timestamp, format = 'YYYY-MM-DD HH:mm:ss') {
    return moment.unix(timestamp).tz('Asia/Shanghai').format(format);
  }

  utcTimeStamp() {
    const nowUtc = moment.utc();

    const msFromUnixEpochToNow = nowUtc.valueOf();
    const ticksFromUnixEpochToNow = msFromUnixEpochToNow * 10_000;
    const ticksBetween0001To1970 = 621_355_968_000_000_000;
    return ticksBetween0001To1970 + ticksFromUnixEpochToNow;
  }

  utcSubTimeStamp(day) {
    const nowUtc = moment.utc().subtract(day, 'days');

    const msFromUnixEpochToNow = nowUtc.valueOf();
    const ticksFromUnixEpochToNow = msFromUnixEpochToNow * 10_000;
    const ticksBetween0001To1970 = 621_355_968_000_000_000;
    return ticksBetween0001To1970 + ticksFromUnixEpochToNow;
  }

  timestampToUtc(timestamp, plus8hours = false) {
    const utc = plus8hours
      ? moment.unix(timestamp).utc().add(8, 'hours')
      : moment.unix(timestamp).utc();

    const msFromUnixEpochToNow = utc.valueOf();
    const ticksFromUnixEpochToNow = msFromUnixEpochToNow * 10_000;
    const ticksBetween0001To1970 = 621_355_968_000_000_000;
    return ticksBetween0001To1970 + ticksFromUnixEpochToNow;
  }

  ticksToMoment(ticks) {
    const ticksBetween0001To1970 = 621_355_968_000_000_000;
    const ticksPerMillisecond = 10000;

    const msSinceUnixEpoch =
      (ticks - ticksBetween0001To1970) / ticksPerMillisecond;

    const dateAt8am = moment
      .tz(msSinceUnixEpoch, 'Asia/Shanghai')
      .set({ hour: 8, minute: 0, second: 0, millisecond: 0 });

    const newTicks =
      dateAt8am.utc().valueOf() * ticksPerMillisecond + ticksBetween0001To1970;

    return newTicks;
  }

  replyType(str: string) {
    let ret = 0;
    if (str.includes('Mark一下')) {
      ret = 1;
    } else if (str.includes('看一下')) {
      ret = 2;
    } else if (str.includes('同意')) {
      ret = 3;
    } else if (str.includes('顶楼主')) {
      ret = 4;
    } else if (str.includes('感谢分享')) {
      ret = 5;
    }
    return ret;
  }

  deepTree(list: any[]) {
    const newList: any[] = [];
    const map: any = {};

    const customSort = (a: any, b: any): number => {
      if (a.leaf !== b.leaf) {
        return a.leaf ? 1 : -1;
      }
      return a.OriginalUrl.localeCompare(b.OriginalUrl);
    };

    list.forEach(e => (map[e.ID] = e));

    list.forEach(e => {
      e.leaf = !_.isArray(e.children);
      const parent = map[e.FatherID];
      if (parent) {
        parent.leaf = false;
        (parent.children || (parent.children = [])).push(e);
        parent.children.sort(customSort);
      } else {
        newList.push(e);
      }
    });

    return newList;
  }

  parseCookies(cookieStr, domain) {
    return cookieStr.split(';').map(pair => {
      const [name, ...rest] = pair.trim().split('=');
      const value = rest.join('=');
      return {
        name,
        value,
        domain,
      };
    });
  }

  yearMonthDate() {
    let month = _.padStart(moment().month() + 1, 2, '0');
    let date = _.padStart(moment().date(), 2, '0');
    return `${moment().year()}/${month}/${date}/`;
  }

  yearMonth() {
    let month = _.padStart(moment().month() + 1, 2, '0');
    let date = _.padStart(moment().date(), 2, '0');
    return `${moment().year()}/${month}/`;
  }

  douyinSignUrl(url: string, userAgent: string) {
    const query = url.includes('?') ? url.split('?')[1] : '';
    const xbogus = sign(query, userAgent);
    const newUrl = url + '&X-Bogus=' + xbogus;

    return newUrl;
  }

  async douyinUserUrlParse(url: string, cookie: string) {
    try {
      let match = url.match(/user\/([-\w]+)/);
      if (match) {
        return match[1];
      }

      match = url.match(/https:\/\/v\.douyin\.com\/([\w-]+)\//);
      if (match) {
        const url = match[0];
        const response = await axios.get(url, {
          headers: {
            'User-Agent': this.douyin.agent,
            Referer: this.douyin.referer,
            Cookie: cookie,
          },
          maxRedirects: 0,
          validateStatus: status => {
            return status >= 200 && status < 400;
          },
        });

        const uidMatch = response.data.match(/user\/([-\w]+)/);
        if (uidMatch) {
          return uidMatch[1];
        }
      }
    } catch (err) {
      throw new Error(`douyinUserUrlParse: ${url} ${err.message}`);
    }
  }

  async douyinHomeInfo(url: string, cookie: string) {
    const response = await axios.get(url, {
      headers: {
        'User-Agent': this.douyin.agent,
        Referer: this.douyin.referer,
        Cookie: cookie,
      },
      maxRedirects: 0,
      validateStatus: status => {
        return status >= 200 && status < 400;
      },
    });

    return _.isObject(response.data) ? response.data : {};
  }

  async douyinIdParse(url: string) {
    let match = url.match(/video\/([-\w]+)/);
    if (match) {
      return {
        id: match[1],
        type: 'video',
      };
    }

    match = url.match(/note\/([-\w]+)/);
    if (match) {
      return {
        id: match[1],
        type: 'note',
      };
    }

    match = url.match(/https:\/\/(v|jx)\.douyin\.com\/[a-zA-Z0-9-]+\//);
    if (match) {
      const response = await axios.get(match[0], {
        headers: {
          'User-Agent': this.douyin.agent,
          Referer: this.douyin.referer,
        },
        maxRedirects: 0,
        validateStatus: status => {
          return status >= 200 && status < 400;
        },
      });

      match = response.data.match(/video\/([-\w]+)/);
      if (match) {
        if (response.data.includes('ixigua.com')) {
          return {
            id: match[1],
            type: 'ixigua',
          };
        } else if (response.data.includes('schema_type=37')) {
          return {
            id: match[1],
            type: 'note',
          };
        } else {
          return {
            id: match[1],
            type: 'video',
          };
        }
      }

      match = response.data.match(/note\/([-\w]+)/);
      if (match) {
        return {
          id: match[1],
          type: 'note',
        };
      }

      match = response.data.match(/user\/([-\w]+)/);
      if (match) {
        return {
          id: match[1],
          type: 'user',
        };
      }
    }

    match = url.match(/^https:\/\/www\.douyin\.com\/ixigua\/(\d+)$/);
    if (match) {
      return {
        id: match[1],
        type: 'ixigua',
      };
    }

    throw new Error('解析抖音链接异常');
  }

  async douyinUrlParse(id: string, cookie: string) {
    const webid = await this.get_webid();
    const msToken = await this.get_msToken();
    const verifyFp = this.get_verify_fp();

    const query = `device_platform=webapp&aid=6383&channel=channel_pc_web&update_version_code=170400&pc_client_type=1&version_code=190500&version_name=19.5.0&cookie_enabled=true&screen_width=2560&screen_height=1440&browser_language=zh-CN&browser_platform=MacIntel&browser_name=Chrome&browser_version=*********&browser_online=true&engine_name=Blink&engine_version=*********&os_name=Mac+OS&os_version=10.15.7&cpu_core_num=6&device_memory=8&platform=PC&downlink=10&effective_type=4g&round_trip_time=50&webid=${webid}&msToken=${msToken}&aweme_id=${id}`;
    const a_bogus = generate_a_bogus(query, this.douyin.agent);
    const url = `https://www.douyin.com/aweme/v1/web/aweme/detail/?${query}&a_bogus=${a_bogus}&verifyFp=${verifyFp}&fp=${verifyFp}`;

    const response = await axios.get(url, {
      headers: {
        'User-Agent': this.douyin.agent,
        Referer: this.douyin.referer,
        Cookie: cookie,
      },
      maxRedirects: 0,
      validateStatus: status => {
        return status >= 200 && status < 400;
      },
    });

    return response.data;
  }

  async douyinRealUrl(url: string) {
    const response = await axios.get(url, {
      headers: {
        'User-Agent': this.douyin.agent,
        Referer: this.douyin.referer,
      },
      maxRedirects: 0,
      validateStatus: status => {
        return status >= 200 && status < 400;
      },
    });

    return response.data;
  }

  async get_msToken() {
    const headers = {
      'User-Agent': this.douyin.agent,
    };
    const api = 'https://mssdk.bytedance.com/web/report';
    const data = {
      magic: 538969122,
      version: 1,
      dataType: 8,
      strData:
        '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',
      tspFromClient: Date.now(),
    };

    try {
      const response = await axios.post(api, data, {
        headers: headers,
        timeout: 10000,
      });

      const s = response.headers['set-cookie'];
      return this.getMsToken(s[0].split(';', 1)[0]);
    } catch (error) {
      throw new Error('提取 msToken 参数失败！');
    }
  }

  async get_webid() {
    const headers = {
      'User-Agent': this.douyin.agent,
    };
    const api = 'https://mcs.zijieapi.com/webid';
    const data = {
      app_id: 6383,
      url: 'https://www.douyin.com/',
      user_agent: this.douyin.agent,
      referer: 'https://www.douyin.com/',
      user_unique_id: '',
    };

    try {
      const response = await axios.post(api, data, {
        headers: headers,
        timeout: 10000,
      });

      return response.data.web_id;
    } catch (error) {
      throw new Error('提取 webid 参数失败！');
    }
  }

  getMsToken(cookieString) {
    const cookies = cookieString.split('; ');
    const msTokenCookie = cookies.find(cookie => cookie.startsWith('msToken='));
    return msTokenCookie ? msTokenCookie.split('=')[1] : '';
  }

  get_verify_fp() {
    const e = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz';
    const t = e.length;

    let milliseconds = Date.now();
    let base36 = '';
    while (milliseconds > 0) {
      const remainder = milliseconds % 36;
      if (remainder < 10) {
        base36 = remainder + base36;
      } else {
        base36 =
          String.fromCharCode('a'.charCodeAt(0) + remainder - 10) + base36;
      }
      milliseconds = Math.floor(milliseconds / 36);
    }

    const r = base36;
    const o = new Array(36).fill('');
    o[8] = o[13] = o[18] = o[23] = '_';
    o[14] = '4';

    for (let i = 0; i < 36; i++) {
      if (!o[i]) {
        let n = Math.floor(Math.random() * t);
        if (i === 19) {
          n = (3 & n) | 8;
        }
        o[i] = e[n];
      }
    }
    return `verify_${r}_` + o.join('');
  }

  async runShellCommand(cmd: string) {
    return await exec(cmd);
  }

  async downloadFile(
    url: string,
    filePath: string,
    fileName: string,
    cookie: string
  ) {
    await fse.ensureDir(filePath);

    const writer = createWriteStream(`${filePath}${fileName}`);

    const response = await axios.get(url, {
      headers: {
        'User-Agent': this.douyin.agent,
        Referer: this.douyin.referer,
        Cookie: cookie,
      },
      responseType: 'stream',
    });

    response.data.pipe(writer);

    return new Promise((resolve, reject) => {
      writer.on('finish', resolve);
      writer.on('error', reject);
    });
  }

  async base64ToImage(txt: string, path) {
    await fse.outputFile(path, txt, { encoding: 'base64' });
  }

  async downloadDouyin(url: string) {
    const response = await axios.get(url, { responseType: 'arraybuffer' });
    return response.data;
  }

  async genMessages4GPT(text, files, role = 'human') {
    const content: any = [
      {
        type: 'text',
        text,
      },
    ];

    if (files.length) {
      for (const url of files) {
        let dest = undefined;
        if (this.ctx.app.getEnv() === 'prod') {
          const myURL = new URL(url.url);
          dest = `${this.baseDir}/../${myURL.pathname}`;
        } else {
          const filename = url.url.split('/').pop();
          dest = `${this.baseDir}/../upload/chatgpt/${filename.replaceAll(
            '-',
            '/'
          )}`;
        }

        const imageData = await fs.readFile(dest);
        const mimeType = mime.lookup(dest);

        content.push({
          type: 'image_url',
          image_url: {
            url: `data:${mimeType};base64,${imageData.toString('base64')}`,
          },
        });
      }

      return role === 'human'
        ? new HumanMessage({ content })
        : new AIMessage({ content });
    } else {
      return role === 'human' ? new HumanMessage(text) : new AIMessage(text);
    }
  }

  /**
   * 获得最近几天的日期集合
   * @param recently
   */
  getRecentlyDates(recently, format = 'YYYY-MM-DD') {
    moment.locale('zh-cn');
    const dates = [];
    for (let i = 0; i < recently; i++) {
      dates.push(moment().subtract(i, 'days').format(format));
    }
    return dates.reverse();
  }
  /**
   * 获得最近几个月的月数
   * @param recently
   */
  getRecentlyMonths(recently, format = 'YYYY-MM') {
    moment.locale('zh-cn');
    const dates = [];
    const date = moment(Date.now()).format('YYYY-MM');
    for (let i = 0; i < recently; i++) {
      dates.push(moment(date).subtract(i, 'months').format(format));
    }
    return dates.reverse();
  }

  /**
   * 根据开始和结束时间，获得时间段内的日期集合
   * @param start
   * @param end
   */
  getBetweenDays(start, end, format = 'YYYY-MM-DD') {
    moment.locale('zh-cn');
    const dates = [];
    const startTime = moment(start).format(format);
    const endTime = moment(end).format(format);
    const days = moment(endTime).diff(moment(startTime), 'days');
    for (let i = 0; i <= days; i++) {
      dates.push(moment(startTime).add(i, 'days').format(format));
    }
    return dates;
  }

  /**
   * 根据开始和结束时间，获得时间段内的月份集合
   * @param start
   * @param end
   */
  getBetweenMonths(start, end, format = 'YYYY-MM') {
    moment.locale('zh-cn');
    const dates = [];
    const startTime = moment(start).format(format);
    const endTime = moment(end).format(format);
    const months = moment(endTime).diff(moment(startTime), 'months');
    for (let i = 0; i <= months; i++) {
      dates.push(moment(startTime).add(i, 'months').format(format));
    }
    return dates;
  }

  /**
   * 根据开始和结束时间，获得时间段内的小时集合
   * @param start
   * @param end
   */
  getBetweenHours(start, end, format = 'YYYY-MM-DD HH') {
    moment.locale('zh-cn');
    const dates = [];
    const startTime = moment(start).format(format);
    const endTime = moment(end).format(format);
    const hours = moment(endTime).diff(moment(startTime), 'hours');
    for (let i = 0; i <= hours; i++) {
      dates.push(moment(startTime).add(i, 'hours').format(format));
    }
    return dates;
  }

  /**
   * 字段转驼峰法
   * @param obj
   * @returns
   */
  toCamelCase(obj) {
    let camelCaseObject = {};
    for (let i in obj) {
      let camelCase = i.replace(/([-_][a-z])/gi, $1 => {
        return $1.toUpperCase().replace('-', '').replace('_', '');
      });
      camelCaseObject[camelCase] = obj[i];
    }
    return camelCaseObject;
  }

  getDZAvatarPath(uid) {
    uid = Math.abs(parseInt(uid, 10));
    const uidString = uid.toString().padStart(9, '0');

    const dir1 = uidString.substring(0, 3);
    const dir2 = uidString.substring(3, 5);
    const dir3 = uidString.substring(5, 7);
    let big = `${uidString.substring(7)}_avatar_big.jpg`;
    let middle = `${uidString.substring(7)}_avatar_middle.jpg`;
    let small = `${uidString.substring(7)}_avatar_small.jpg`;

    big = `${this.baseDir}/../forum_avatar/${dir1}/${dir2}/${dir3}/${big}`;
    middle = `${this.baseDir}/../forum_avatar/${dir1}/${dir2}/${dir3}/${middle}`;
    small = `${this.baseDir}/../forum_avatar/${dir1}/${dir2}/${dir3}/${small}`;

    return [big, middle, small];
  }

  async delDZAvatar(uid) {
    const files = this.getDZAvatarPath(uid);

    for (const file of files) {
      const exist = await this.fileExists(file);
      if (exist) await fse.move(file, `${file}.del`);
    }
  }

  async fileExists(filePath) {
    try {
      await fs.access(filePath);
      return true;
    } catch (error) {
      return false;
    }
  }

  shortId() {
    return nanoid(8);
  }

  resizeSpec(source, target, spec, ext) {
    return new Promise((resolve, reject) => {
      for (let i = 0; i < spec.length; i++) {
        let wh = spec[i];
        const filename = spec.length === 1 ? target : `${target}/${wh.w}`;

        sharp(source)
          .resize(wh.w, wh.h)
          .toFile(`${filename}.${ext}`)
          .then(data => {
            resolve(data);
          })
          .catch(err => {
            reject(err);
          });
      }
    });
  }

  replaceLinksForPush(html, type) {
    const regex =
      /(<a\s+(?:[^>]*?\s+)?href="https:\/\/go\.chasedream\.com[^"]*)(?<!\?[^"]*)"/g;

    return html.replace(regex, `$1?c=${type}"`);
  }

  /**
   * 从论坛URL中提取帖子ID
   * @param url 论坛URL，例如：https://forum.chasedream.com/thread-1397043-1-1.html
   * @returns 提取的帖子ID，例如：1397043
   */
  extractForumTid(url: string) {
    if (!url) return 0;
    
    // 如果输入是数字，直接返回该数字
    if (/^\d+$/.test(url)) return url;
    
    const match = url.match(/thread-(\d+)/);
    return match ? match[1] : '';
  }
}
