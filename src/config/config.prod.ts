import { CoolConfig } from '@cool-midway/core';
import { MODETYPE } from '@cool-midway/file';
import { MidwayConfig } from '@midwayjs/core';
import { createRedisStore } from '@midwayjs/cache-manager';

/**
 * 本地开发 npm run prod 读取的配置文件
 */
export default {
  aliyun: {
    greenCip: {
      url: 'https://green-cip-vpc.cn-beijing.aliyuncs.com'
    } 
  },
  noticeType: {
    email: '<EMAIL>',
  },
  rss: {
    email: '<EMAIL>',
  },
  wechaty: {
    puppet: 'wechaty-puppet-padlocal',
    puppetOptions: {
      token: 'puppet_padlocal_a05801ff24de4cb7bc97e48a628e2cdf',
    },
    name: 'wechatBot',
    enable: false,
  },
  cacheManager: {
    clients: {
      default: {
        store: createRedisStore('default'),
      },
    },
  },
  redis: {
    clients: {
      default: {
        host: '*********',
        port: 6379,
        password: 'stap-^Rexat3us7uk_ab',
        ttl: null,
        db: 8,
      },
    },
  },
  bull: {
    defaultQueueOptions: {
      redis: {
        host: '*********',
        port: 6379,
        password: 'stap-^Rexat3us7uk_ab',
        db: 8,
      },
    },
  },
  typeorm: {
    dataSource: {
      default: {
        type: 'mysql',
        host: '*********',
        port: 3306,
        username: 'conn_apps_connect',
        password: 'v9AbG7rrhNbkcvpk',
        database: 'site_apps_connect',
        // 自动建表 注意：线上部署的时候不要使用，有可能导致数据丢失
        synchronize: false,
        // 打印日志
        logging: false,
        // 字符集
        charset: 'utf8mb4',
        cache: true,
        entities: ['**/modules/*/entity'],
      },
      forum: {
        type: 'mysql',
        host: '********',
        port: 3306,
        username: 'conn_forum',
        password: 'ZqA5Kv7tsr2R24RV',
        database: 'forum',
        // 自动建表 注意：线上部署的时候不要使用，有可能导致数据丢失
        synchronize: false,
        // 打印日志
        logging: false,
        // 字符集
        charset: 'utf8mb4',
        entities: ['**/modules/*/entity/forum'],
      },
      chiji: {
        type: 'mysql',
        host: '********',
        port: 3306,
        username: 'conn_gmat_chiji',
        password: 'EHnt7sg0wLEOAmNd',
        database: 'site_gmat_chiji',
        // 自动建表 注意：线上部署的时候不要使用，有可能导致数据丢失
        synchronize: false,
        // 打印日志
        logging: false,
        // 字符集
        charset: 'utf8mb4',
        entities: ['**/modules/*/entity/chiji'],
      },
      id: {
        type: 'mysql',
        host: '********',
        port: 3306,
        username: 'conn_apps_id',
        password: 'Qxfh4AsAyJ1Rit3L',
        database: 'site_apps_id',
        // 自动建表 注意：线上部署的时候不要使用，有可能导致数据丢失
        synchronize: false,
        // 打印日志
        logging: false,
        // 字符集
        charset: 'utf8mb4',
        entities: ['**/modules/*/entity/event', '**/modules/*/entity/tag'],
      },
      go: {
        type: 'mssql',
        host: '*********',
        port: 1433,
        username: 'conn_GO',
        password: 'Deh9_@#@cqUu#k!S',
        database: 'Site_GO',
        options: {
          encrypt: false,
          trustServerCertificate: true,
        },
        // 自动建表 注意：线上部署的时候不要使用，有可能导致数据丢失
        synchronize: false,
        // 打印日志
        logging: false,
        // 字符集
        charset: 'utf8mb4',
        entities: ['**/modules/*/entity/go'],
      },
      www: {
        type: 'mssql',
        host: '*********',
        port: 1433,
        username: 'conn_www',
        password: 'QazQMMs89KEBzN6zxgb2d',
        database: 'Site_WWW',
        options: {
          encrypt: false,
          trustServerCertificate: true,
        },
        // 自动建表 注意：线上部署的时候不要使用，有可能导致数据丢失
        synchronize: false,
        // 打印日志
        logging: false,
        // 字符集
        charset: 'utf8mb4',
        entities: ['**/modules/*/entity/www'],
      },
      wwwbuffer: {
        type: 'mssql',
        host: '*********',
        port: 1433,
        username: 'conn_WWWBuffer',
        password: 'C7fC4HJS*U#n',
        database: 'Site_WWWBuffer',
        options: {
          encrypt: false,
          trustServerCertificate: true,
        },
        // 自动建表 注意：线上部署的时候不要使用，有可能导致数据丢失
        synchronize: false,
        // 打印日志
        logging: false,
        // 字符集
        charset: 'utf8mb4',
        entities: ['**/modules/*/entity/wwwbuffer'],
      },
    },
  },
  cool: {
    file: {
      // 上传模式 本地上传或云存储
      mode: MODETYPE.LOCAL,
      // 本地上传 文件地址前缀
      domain: 'https://connect.chasedream.com',
    },
    es: {
      // 集群模式下可以配置多个地址
      nodes: ['http://elastic:8HdiGD+CZ3Rf0rOFl*Zk@*********:9200'],
    },
    eps: false,
    initDB: false,
    initMenu: false,
  } as CoolConfig,
} as MidwayConfig;
