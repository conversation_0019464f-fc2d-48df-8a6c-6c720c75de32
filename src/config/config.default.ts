import { CoolConfig } from '@cool-midway/core';
import { MODETYPE } from '@cool-midway/file';
import { MidwayConfig } from '@midwayjs/core';
import { uploadWhiteList } from '@midwayjs/upload';

export default {
  chatgpt: {
    apiKey: '***************************************************',
    apiBaseUrl: 'https://us.ladderfit.space/chatgpt/v1',
    completionParams: {
      temperature: 0.3,
    },
    timeoutMs: 60 * 1000,
    debug: true,
    context: 6,
    assistantId: 'asst_8fbOWa9qvv1UAgyiaVwyyhxR',
  },
  moonshot: {
    apiKey: 'sk-bGHQHVJWcCelXgGJvlxdLOi2n7MzfmzCGqNygPZn2aPkiqF0',
    apiBaseUrl: 'https://api.moonshot.cn/v1',
    completionParams: {
      temperature: 0.3,
    },
    timeoutMs: 60 * 1000,
    debug: true,
    context: 6,
  },
  anthropic: {
    apiKey:
      '************************************************************************************************************',
    apiBaseUrl: 'https://us.ladderfit.space/anthropic',
    completionParams: {
      temperature: 0.3,
    },
    timeoutMs: 60 * 1000,
    debug: true,
    context: 6,
  },
  // 阿里云 oss
  oss: {
    client: {
      accessKeyId: 'LTAI5t6yMnFUNPYotjns99Rx',
      accessKeySecret: '******************************',
      bucket: 'connect-chasedream-com-douyin',
      endpoint: 'oss-cn-beijing.aliyuncs.com',
      timeout: '60s',
    },
  },
  discuz: {
    prefix: 'jcsA_d25e_',
    authkey: 'dca5995vn1beqYxI',
    homeUrl: 'https://forum.chasedream.com',
    domain: '.chasedream.com',
    apiBaseUrl:
      'https://forum.chasedream.com/api/mobile/index.php?mobile=no&version=1',
    highlightDigest:
      'https://forum.chasedream.com/forum.php?mod=topicadmin&action=moderate&optgroup=1&modsubmit=yes&infloat=yes&inajax=1',
    deleteThread:
      'https://forum.chasedream.com/forum.php?mod=topicadmin&action=moderate&optgroup=3&modsubmit=yes&infloat=yes&inajax=1',
    stick:
      'https://forum.chasedream.com/forum.php?mod=topicadmin&action=moderate&optgroup=1&modsubmit=yes&infloat=yes&inajax=1',
    sendPost:
      'https://forum.chasedream.com/forum.php?mod=post&action=reply&extra=page%3D1&replysubmit=yes&infloat=yes&handlekey=fastpost&inajax=1',
    cache:
      'https://forum.chasedream.com/admin.php?action=setting&operation=memory&do=clear',
    cookieMaxAge: 1000 * 60 * 60 * 24 * 30,
    limitFid: [
      9, 55, 44, 106, 32, 12, 21, 57, 94, 56, 42, 72, 75, 98, 79, 84, 33, 30,
      64, 43, 50,
    ],
  },
  redisKey: {
    eventList: 'eventList',
    forumGroup: 'forumGroup',
    portalBannerIndex: 'portalBannerIndex',
  },
  custom: {
    ngrok: {
      domain:
        'https://efb5-2409-8a00-603c-1291-ecc0-e731-272d-5227.ngrok-free.app',
    },
  },
  // 修改成你自己独有的key
  keys: 'UUcb4P8Y}i=Q3L3#',
  koa: {
    port: 8002,
  },
  bodyParser: {
    enableTypes: ['json', 'form', 'text', 'xml'],
    formLimit: '50mb',
    jsonLimit: '50mb',
    textLimit: '50mb',
    xmlLimit: '50mb',
  },
  task: {
    redis: {
      port: 6379,
      host: '**************',
      password: 'C7B#cI{jKng5SpZi',
    },
    prefix: 'midway-task',
    defaultJobOptions: {
      repeat: {
        tz: 'Asia/Shanghai',
      },
    },
  },
  // 文件上传
  upload: {
    fileSize: '200mb',
    whitelist: [
      ...uploadWhiteList,
      '.doc',
      '.docx',
      '.xls',
      'xlsx',
      'ppt',
      'pptx',
    ],
  },
  cors: {
    credentials: true,
  },
  jsonp: {
    callback: 'callback',
    limit: 512,
  },
  // 模板渲染
  view: {
    mapping: {
      '.html': 'ejs',
    },
  },
  // 静态文件配置
  staticFile: {
    buffer: true,
  },
  // 听悟
  tingwu: {
    appKey: 'uiof6qEKCJyNY1EC',
    endpoint: 'tingwu.cn-beijing.aliyuncs.com',
    audioLanguage: 'cn',
  },
  // 抖音
  douyin: {
    agent:
      'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    referer: 'https://www.douyin.com/',
    width: 1080,
    height: 1024,
    ssologin: {
      qr: 'https://sso.douyin.com/get_qrcode/?',
      checkQr: 'https://sso.douyin.com/check_qrconnect/?',
    },
  },
  // 企业微信
  wecom: {
    corpid: 'wwa20134d3ffd8083c',
    corpsecret: 'u0B6ZlHwJuzTkb9P9AZ-1wr63a6fAgLu5wCMej49yS8',
    customApp: {
      token: 'BJMk2isRvJlMe1',
      EncodingAESKey: 'eXYFXPGfBTjJO5ntb9PcgDwmFufhvBUcbWMn0Iz5Ki1',
    },
  },
  cool: {
    file: {
      // 上传模式 本地上传或云存储
      mode: MODETYPE.LOCAL,
      // 本地上传 文件地址前缀，当且仅当mode为LOCAL时生效
      domain: 'http://127.0.0.1:8002',
    },
    es: {
      // 集群模式下可以配置多个地址
      nodes: ['http://elastic:-d9ortQfavQP*SW+m*QP@**************:9200'],
    },
    // crud配置
    crud: {
      // 插入模式，save不会校验字段(允许传入不存在的字段)，insert会校验字段
      upsert: 'save',
      // 软删除
      softDelete: true,
    },
  } as CoolConfig,
} as MidwayConfig;
