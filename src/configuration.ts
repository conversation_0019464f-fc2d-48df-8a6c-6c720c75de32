import * as orm from '@midwayjs/typeorm';
import { Configuration, App } from '@midwayjs/decorator';
import * as koa from '@midwayjs/koa';
import * as validate from '@midwayjs/validate';
import * as info from '@midwayjs/info';
import { join } from 'path';
import * as staticFile from '@midwayjs/static-file';
import * as view from '@midwayjs/view-ejs';
import * as cool from '@cool-midway/core';
import * as file from '@cool-midway/file';
import * as crossDomain from '@midwayjs/cross-domain';
import * as socketio from '@midwayjs/socketio';
import * as cron from '@midwayjs/cron';
import * as cacheManager from '@midwayjs/cache-manager';
import * as redis from '@midwayjs/redis';
// import * as pay from '@cool-midway/pay';
// import * as rpc from '@cool-midway/rpc';
import * as es from '@cool-midway/es';
import * as oss from '@midwayjs/oss';
import * as bull from '@midwayjs/bull';
import { WSMiddleware } from './modules/base/middleware/ws';
import { BaseSysConfEntity } from './modules/base/entity/sys/conf';
import { InjectEntityModel } from '@midwayjs/typeorm';
import { Repository } from 'typeorm';
import * as upload from '@midwayjs/upload';
import { IMidwayApplication } from '@midwayjs/core';

@Configuration({
  imports: [
    // https://koajs.com/
    koa,
    // 是否开启跨域(注：顺序不能乱放！！！) http://www.midwayjs.org/docs/extensions/cross_domain
    crossDomain,
    // 模板渲染 https://midwayjs.org/docs/extensions/render
    view,
    // 静态文件托管 https://midwayjs.org/docs/extensions/static_file
    staticFile,
    // orm https://midwayjs.org/docs/extensions/orm
    orm,
    // 参数验证 https://midwayjs.org/docs/extensions/validate
    validate,
    // 本地任务 http://www.midwayjs.org/docs/extensions/cron
    cron,
    // 文件上传
    upload,
    // cool-admin 官方组件 https://cool-js.com
    cool,
    // 文件上传 阿里云存储 腾讯云存储 七牛云存储
    file,
    // 阿里云OSS
    oss,
    // 任务与队列
    // task,
    cacheManager,
    // 支付 微信与支付宝
    // pay,
    // 分布式任务队列
    bull,
    // elasticsearch
    es,
    redis,
    socketio,
    // rpc 微服务 远程调用
    // rpc,
    // swagger 文档  http://www.midwayjs.org/docs/extensions/swagger
    // swagger,
    {
      component: info,
      enabledEnvironment: ['local'],
    },
  ],
  importConfigs: [join(__dirname, './config')],
})
export class ContainerLifeCycle {
  @App()
  app: IMidwayApplication;

  @App('socketIO')
  wsApp: socketio.Application;

  @InjectEntityModel(BaseSysConfEntity)
  baseSysConfEntity: Repository<BaseSysConfEntity>;

  async onReady() {
    this.wsApp.useMiddleware(WSMiddleware);
  }
}
