import { Controller, Get, Inject } from '@midwayjs/decorator';
import { Context } from '@midwayjs/koa';
import { Param } from '@midwayjs/decorator';
import { InjectEntityModel } from '@midwayjs/typeorm';
import { Repository } from 'typeorm';
import { Utils } from './comm/utils';
import { TblEventApplyCalendar } from './modules/base/entity/event/TblEventApply';
import * as QRCode from 'qrcode';

/**
 * 欢迎界面
 */
@Controller('/')
export class WelcomeController {
  @Inject()
  ctx: Context;

  @Inject()
  utils: Utils;

  @InjectEntityModel(TblEventApplyCalendar, 'id')
  tblEventApplyCalendar: Repository<TblEventApplyCalendar>;

  @Get('/', { summary: '欢迎界面' })
  public async welcome() {
    return '';
  }

  @Get('/details/:id?')
  async searchEvents(@Param('id') id?: number) {
    if (id <= 0) {
      this.ctx.status = 404;
      return;
    }

    const res: any = await this.tblEventApplyCalendar.findOneBy({
      id: id.toString(),
    });

    if (!res) {
      this.ctx.status = 404;
      return;
    }

    res.qrcode = await QRCode.toDataURL(`https://cal.top.mba/details/${id}`);

    res.created_at = this.utils.timestampToDate(
      res.created_at,
      'YYYY-MM-DD HH:mm'
    );

    await this.ctx.render('applyCalendar/index', res);
  }
}
