import { Middleware } from '@midwayjs/decorator';
import { resolve } from 'path';
import * as _ from 'lodash';
import { NextFunction, Context } from '@midwayjs/koa';
import { IMiddleware } from '@midwayjs/core';
import { readFile } from 'fs/promises';
import { EventService } from '../service/common/event';

/**
 * Go模块中间件
 */
@Middleware()
export class GoMiddleware implements IMiddleware<Context, NextFunction> {
  resolve() {
    return async (ctx: Context, next: NextFunction) => {
      const types = ['p', 'f', 'c', 'wx', 'hb', 'iesecn'];
      const eventService = await ctx.requestContext.getAsync(EventService);

      const host = ctx.request.header.host;
      const regex = /\/(e|s)\/([^/?]+)(?:\?c=([^&]+))?/;

      let domain =
        ctx.app.getEnv() === 'prod'
          ? ['go.chasedream.com']
          : ['go.chasedream.com:8002'];

      if (!domain.includes(host)) {
        await next();
        return;
      }

      const match = ctx.request.url.match(regex);
      if (match && match[1] && match[2]) {
        const pathType = match[1];
        const path = match[2];
        const type = types.includes(match[3]) ? match[3] : 'other';

        const url = await eventService.getUrlFromPath(path);
        if (url) {
          if (ctx.method === 'GET') {
            const filePath = resolve(
              __dirname,
              '../../../../view/redirect.html'
            );
            const file = await readFile(filePath, 'utf-8');
            const protocol = ctx.app.getEnv() === 'prod' ? 'https' : 'http';

            ctx.body = file.replace(
              '{{url}}',
              `${protocol}://${host}${ctx.url}`
            );
          } else if (ctx.method === 'POST') {
            await eventService.externalLinkLogAdd({
              external_id: url.id,
              type,
              // @ts-ignore
              resolution: `${ctx.request.body.ScreenWidth}x${ctx.request.body.ScreenHeight}`,
            });

            if (pathType === 's' && url.targetUrl.includes('\n')) {
              const isMobile = /Mobile|Android|iPhone/i.test(
                ctx.get('User-Agent') || ''
              );
              const urls = url.targetUrl.split('\n');
              const targetUrl = isMobile ? urls[0] : urls[1] || urls[0];

              ctx.redirect(targetUrl);
            } else {
              ctx.redirect(url.targetUrl);
            }
          }
        } else {
          ctx.redirect('https://www.chasedream.com');
        }
      } else {
        ctx.redirect('https://www.chasedream.com');
      }
    };
  }
}
