import { Middleware } from '@midwayjs/decorator';
import { resolve } from 'path';
import * as _ from 'lodash';
import { NextFunction, Context } from '@midwayjs/koa';
import { IMiddleware } from '@midwayjs/core';
import { GoService } from '../service/common/go';
import { readFile } from 'fs/promises';

/**
 * G模块中间件
 */
@Middleware()
export class GMiddleware implements IMiddleware<Context, NextFunction> {
  resolve() {
    return async (ctx: Context, next: NextFunction) => {
      const goService = await ctx.requestContext.getAsync(GoService);

      const host = ctx.request.header.host;
      const orgPath = ctx.request.url.replace(/^\/|\/$/g, '');
      const [path, query] = orgPath.split('?');
      
      let domain =
        ctx.app.getEnv() === 'prod'
          ? ['g.chasedream.com', 'i.mba']
          : ['g.chasedream.com:8002', 'i.mba:8002'];

      if (!domain.includes(host)) {
        await next();
        return;
      }

      const url = await goService.getUrlFromPath(path);
      if (url) {
        if (ctx.method === 'GET') {
          const filePath = resolve(__dirname, '../../../../view/redirect.html');
          const file = await readFile(filePath, 'utf-8');
          const protocol = ctx.app.getEnv() === 'prod' ? 'https' : 'http';

          ctx.body = file.replace('{{url}}', `${protocol}://${host}${ctx.url}`);
        } else if (ctx.method === 'POST') {
          await goService.urlDetailAdd({
            OriginalUrl: path,
            TargetUrl: url.TargetUrl,
            // @ts-ignore
            Resolution: `${ctx.request.body.ScreenWidth}x${ctx.request.body.ScreenHeight}`,
            query: query || ''
          });
          ctx.redirect(url.TargetUrl);
        }
      } else {
        ctx.redirect('https://www.chasedream.com');
      }
    };
  }
}
