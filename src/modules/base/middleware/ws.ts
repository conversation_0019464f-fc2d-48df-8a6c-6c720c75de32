import { App, Config, Inject, Middleware } from '@midwayjs/decorator';
import * as _ from 'lodash';
import * as jwt from 'jsonwebtoken';
import { NextFunction, Context } from '@midwayjs/socketio';
import { IMiddleware, IMidwayApplication } from '@midwayjs/core';
import { CacheManager } from '@midwayjs/cache';

/**
 * 权限校验
 */
@Middleware()
export class WSMiddleware implements IMiddleware<Context, NextFunction> {
  @Config('koa.globalPrefix')
  prefix;

  @Config('module.base')
  jwtConfig;

  @Inject()
  cacheManager: CacheManager;

  @App()
  app: IMidwayApplication;

  resolve() {
    return async (ctx: Context, next: NextFunction) => {
      const token = ctx.handshake.auth.token;
      try {
        // @ts-ignore
        ctx.admin = jwt.verify(token, this.jwtConfig.jwt.secret);
      } catch (err) {
        console.log(err.message);
        ctx.disconnect();
        return;
      }

      return await next();
    };
  }
}
