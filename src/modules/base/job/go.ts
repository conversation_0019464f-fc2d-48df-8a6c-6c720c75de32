import { Processor, IProcessor } from '@midwayjs/bull';
import { FORMAT, Inject } from '@midwayjs/decorator';
import { ILogger } from '@midwayjs/logger';
import { Utils } from '../../../comm/utils';
import { InjectEntityModel } from '@midwayjs/typeorm';
import { Repository } from 'typeorm';
import { Url } from '../entity/go/Url';
import { UrlDetails } from '../entity/go/UrlDetails';
import { UrlStatistics } from '../entity/go/UrlStatistics';

/**
 * Go定时任务
 */
@Processor('StatisticsProcessor', {
  repeat: {
    cron: FORMAT.CRONTAB.EVERY_DAY_ONE_FIFTEEN,
  },
})
export class StatisticsProcessor implements IProcessor {
  @InjectEntityModel(Url, 'go')
  url: Repository<Url>;

  @InjectEntityModel(UrlDetails, 'go')
  urlDetails: Repository<UrlDetails>;

  @InjectEntityModel(UrlStatistics, 'go')
  urlStatistics: Repository<UrlStatistics>;

  @Inject()
  utils: Utils;

  @Inject()
  logger: ILogger;

  async execute() {
    const date = this.utils.ticksToMoment(this.utils.utcSubTimeStamp(1));
    const createTime = date.toString();

    const urls = await this.url.find({
      where: {
        Enable: true,
        IsDeleted: false,
      },
    });

    for (let url of urls) {
      const hits = await this.urlDetails.count({
        where: {
          Date: date.toString(),
          IsDeleted: false,
          OriginalUrl: url.OriginalUrlPath,
        },
      });

      try {
        await this.urlStatistics.save({
          OriginalUrl: url.OriginalUrlPath,
          Hits: hits,
          Year: this.utils.yesterday('YYYY'),
          Month: this.utils.yesterday('YYYY.MM'),
          Week: this.utils.week('YYYY.MM.DD', 'YYYY.MM.DD'),
          CreateTime: createTime,
        });
      } catch (error) {
        this.logger.error(error.message);
      }
    }
  }
}
