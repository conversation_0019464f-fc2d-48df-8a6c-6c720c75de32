import { <PERSON>, IJob } from '@midwayjs/cron';
import { FORMAT, Inject } from '@midwayjs/core';
import { Utils } from '../../../comm/utils';
import { EventService } from '../service/common/event';

/**
 * 活动发布
 */
@Job({
  cronTime: FORMAT.CRONTAB.EVERY_MINUTE,
  start: true,
})
export class EventJob implements IJob {
  @Inject()
  eventService: EventService;

  @Inject()
  utils: Utils;

  async onTick() {
    await this.eventService.calendarClean();
    await this.eventService.releaseClean();
    await this.eventService.ieseClean();
  }
}
