import { <PERSON>, <PERSON>Job } from '@midwayjs/cron';
import { FORMAT, Inject } from '@midwayjs/core';
import { ChatOpenAI } from '@langchain/openai';
import { HumanMessage } from '@langchain/core/messages';
import { Config } from '@midwayjs/decorator';
import { EmailService } from '../service/common/email';
import { Utils } from '../../../comm/utils';

/**
 * Rss定时任务
 */
@Job({
  cronTime: FORMAT.CRONTAB.EVERY_DAY_ZERO_FIFTEEN,
  start: true,
})
export class RssJob implements IJob {
  @Inject()
  emailService: EmailService;

  @Config('chatgpt')
  chatgpt;

  @Config('rss')
  rss;

  @Inject()
  utils: Utils;

  async onTick() {
    const chat = new ChatOpenAI({
      apiKey: this.chatgpt.apiKey,
      model: 'gpt-3.5-turbo',
      temperature: 0,
      configuration: {
        baseURL: this.chatgpt.apiBaseUrl,
        maxRetries: 5,
      },
    }).bind({
      response_format: {
        type: 'json_object',
      },
    });

    let Parser = require('rss-parser');
    let parser = new Parser();

    const feed = await parser.parseURL('https://poetsandquants.com/feed/');
    const arr = [];

    for (const item of feed.items) {
      const res = await chat.invoke([
        new HumanMessage(`翻译这个JSON数组中的title和content为中文:
        title: ${item.title}
        link: ${item.link}
        content: ${item.contentSnippet}
          `),
      ]);

      arr.push(JSON.parse(res?.content.toString()));
    }

    if (arr.length === 0) return;

    let message = '';
    for (const obj of arr) {
      message += `${obj.title}<br />
       ${obj.content}<br />
       <a href="${obj.link}" target="_blank">${obj.link}</a><br />
       <br /><br />
      `;
    }

    await this.emailService.sendmail({
      toemail: this.rss.email,
      subject: `Poets & Quants 简报 - ${this.utils.today('YYYY-MM-DD')}`,
      message,
    });
  }
}
