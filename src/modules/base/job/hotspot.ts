import { <PERSON>, IJob } from '@midwayjs/cron';
import { Inject } from '@midwayjs/decorator';
import { InjectEntityModel } from '@midwayjs/typeorm';
import { Brackets, Repository } from 'typeorm';
import { CommonHotspotEntity } from '../entity/common/hotspot';
import { CommonHotspotQueueEntity } from '../entity/common/hotspotQueue';
import { Utils } from '../../../comm/utils';
import * as _ from 'lodash';

/**
 * 热点定时任务
 */
@Job({
  cronTime: '0 0 0 * * *',  
  start: true,
})
export class HotspotJob implements IJob {
  @InjectEntityModel(CommonHotspotEntity)
  commonHotspotEntity: Repository<CommonHotspotEntity>;

  @InjectEntityModel(CommonHotspotQueueEntity)
  commonHotspotQueueEntity: Repository<CommonHotspotQueueEntity>;

  @Inject()
  utils: Utils;

  async onTick() {
    await this.commonHotspotQueueEntity.query(
      'TRUNCATE TABLE common_hotspot_queue'
    );

    const currentTime = this.utils.now();

    const query = this.commonHotspotEntity
      .createQueryBuilder('hotspot')
      .where('hotspot.begin < :currentTime', { currentTime })
      .andWhere(
        new Brackets(qb => {
          qb.where('hotspot.end > :currentTime', { currentTime }).orWhere(
            'hotspot.end = 0'
          );
        })
      )
      .andWhere('hotspot.status = :status', { status: 0 });

    const hotspots = await query.getMany();
    const models = [];

    const nightStartTime = 0;
    const nightEndTime = 7;
    const nightDataPercentage = 0.2;

    for (let hotspot of hotspots) {
      let showTime = _.random(hotspot.showTimeMin, hotspot.showTimeMax);

      const totalDataPoints = showTime;
      const nightDataPoints = Math.round(totalDataPoints * nightDataPercentage);
      const dayDataPoints = totalDataPoints - nightDataPoints;

      let interval =
        _.random(hotspot.showIntervalMin, hotspot.showIntervalMax) * 60;

      for (let i = 0; i < nightDataPoints; i++) {
        let timeOffset = i * interval + nightStartTime * 60 * 60;
        let dataTime = currentTime + timeOffset;

        models.push({
          begin: dataTime,
          targetUrl: hotspot.targetUrl,
          referer: hotspot.referer,
          type: hotspot.type,
          pid: hotspot.id,
        });
      }

      for (let i = 0; i < dayDataPoints; i++) {
        let timeOffset = i * interval + nightEndTime * 60 * 60;
        let dataTime = currentTime + timeOffset;

        models.push({
          begin: dataTime,
          targetUrl: hotspot.targetUrl,
          referer: hotspot.referer,
          type: hotspot.type,
          pid: hotspot.id,
        });
      }
    }

    await this.commonHotspotQueueEntity.save(models);

    // 更新热点状态
    const items = await this.commonHotspotEntity.find({
      where: { status: 0 }
    })

    for (const item of items) {
      if (item.end <= this.utils.now()) {
        await this.commonHotspotEntity.update(item.id, { status: -1 });
      }
    }
  }
}
