import { <PERSON>, <PERSON>Job } from '@midwayjs/cron';
import { Config, FORMAT, Inject, Logger } from '@midwayjs/decorator';
import { BaseSysConfService } from '../service/sys/conf';
import { Utils } from '../../../comm/utils';
import { InjectEntityModel } from '@midwayjs/typeorm';
import { DouyinUrlEntity } from '../entity/douyin/douyinUrl';
import { DouyinAccountEntity } from '../entity/douyin/douyinAccount';
import { DouyinImageEntity } from '../entity/douyin/douyinImage';
import { Repository, MoreThanOrEqual, In } from 'typeorm';
import { DouyinFollowEntity } from '../entity/douyin/douyinFollow';
import { ILogger } from '@midwayjs/logger';
import { InjectClient } from '@midwayjs/core';
import axios from 'axios';
import * as _ from 'lodash';
import { CachingFactory, MidwayCache } from '@midwayjs/cache-manager';
const { generate_a_bogus } = require('../../../comm/a_bogus.js');

/**
 * 抖音下载视频定时任务
 */
@Job({
  cronTime: FORMAT.CRONTAB.EVERY_MINUTE,
  start: true,
})
export class DownloadVideoJob implements IJob {
  @InjectEntityModel(DouyinUrlEntity)
  douyinUrlEntity: Repository<DouyinUrlEntity>;

  @InjectEntityModel(DouyinAccountEntity)
  douyinAccountEntity: Repository<DouyinAccountEntity>;

  @InjectEntityModel(DouyinImageEntity)
  douyinImageEntity: Repository<DouyinImageEntity>;

  @InjectEntityModel(DouyinFollowEntity)
  douyinFollowEntity: Repository<DouyinFollowEntity>;

  @Inject()
  baseSysConfService: BaseSysConfService;

  @Inject()
  utils: Utils;

  @Logger()
  logger: ILogger;

  @InjectClient(CachingFactory, 'default')
  midwayCache: MidwayCache;

  @Inject()
  baseDir;

  @Config('douyin')
  douyin;

  async onTick() {
    let work = (await this.midwayCache.get('douyinWork')) || 0;

    if (work === 1) return;

    await this.midwayCache.set('douyinWork', 1, 1000 * 60 * 5);

    // const isMidnight = this.utils.isMidnight();
    const isMidnight = true;

    const query = this.douyinUrlEntity.createQueryBuilder('douyin_url');
    query.where('douyin_url.status = :status', { status: 0 });

    if (!isMidnight) {
      query
        .andWhere('douyin_url.followId = :followId', { followId: 0 })
        .orWhere('douyin_url.run = :run', { run: 1 });
    }

    query.orderBy('douyin_url.id', 'DESC').take(2);

    const urls = (await query.getMany()) || [];

    for (let url of urls) {
      try {
        const accounts = await this.douyinAccountEntity.find();
        const account = _.sample(accounts);
        const cookie = account.cookie;

        let info: any = {};
        let { id, type } = await this.utils.douyinIdParse(url.url);

        await this.utils.randomSleep(3);

        info = await this.utils.douyinUrlParse(id, cookie);

        if (!info.aweme_detail && type !== 'ixigua') {
          throw new Error(info?.filter_detail?.detail_msg || '解析失败');
        }

        if (type !== 'ixigua') {
          await this.utils.randomSleep(3);

          if (info.status_code === 0) {
            const targetUrl =
              info.aweme_detail.video.play_addr.url_list.find((el: string) =>
                el.includes('v1/play')
              ) || info.aweme_detail.video.play_addr.url_list[0];

            const desc = info.aweme_detail.desc;
            const cover = info.aweme_detail.video.cover.url_list.find(
              (el: string) => el.includes('.jpeg')
            );
            const author = info.aweme_detail.author.nickname;
            const videoDate = info.aweme_detail.create_time;

            url.targetUrl = targetUrl;
            url.remoteCoverUrl = cover;

            await this.douyinUrlEntity.update(
              {
                id: url.id,
              },
              {
                title: desc,
                author,
                type,
                videoDate,
                targetUrl,
                remoteCoverUrl: cover,
                run: 0,
              }
            );
          }
        }

        const yearMonthDate = this.utils.yearMonthDate();
        const filePath = `${this.baseDir}/../upload/douyin/${yearMonthDate}`;
        const fileName = `${this.utils.today('YYYYMMDDHHmmss')}`;
        const extMp3 = '.mp3';
        const extMp4 = '.mp4';
        const extJpeg = '.jpeg';
        // const extTxt = '.txt';

        if (type === 'note') {
          await this.utils.downloadFile(
            info.aweme_detail?.music?.play_url?.url_list[0],
            filePath,
            `${fileName}${extMp3}`,
            cookie
          );

          await this.douyinUrlEntity.update(
            {
              id: url.id,
            },
            {
              localFile: `/upload/douyin/${yearMonthDate}${fileName}${extMp3}`,
            }
          );

          await this.utils.randomSleep(3);

          const images = info.aweme_detail?.images;
          let i = 0;

          for (const image of images) {
            const target = image.url_list.find((el: string) =>
              el.includes('.jpeg')
            );

            await this.utils.downloadFile(
              target,
              filePath,
              `${fileName}-${i}${extJpeg}`,
              cookie
            );

            await this.douyinImageEntity.save({
              urlId: url.id,
              url: `https://connect.chasedream.com/upload/douyin/${yearMonthDate}${fileName}-${i}${extJpeg}`,
            });

            await this.douyinUrlEntity.update(
              {
                id: url.id,
              },
              {
                type,
                status: 1,
                run: 0,
              }
            );

            i++;
            await this.utils.randomSleep(3);
          }
        } else if (type === 'ixigua') {
          let res = await this.parseXiguaUrl(
            `https://m.ixigua.com/douyin/share/video/${id}?aweme_type=107&schema_type=1&utm_source=copy&utm_campaign=client_share&utm_medium=android&app=aweme`
          );

          const regex = /_ROUTER_DATA\s*=\s*(\{.*?\})(?=<\/script>)/;
          const match = res.match(regex);

          if (match && match[1]) {
            let data = JSON.parse(match[1]);

            const item =
              data.loaderData['video_(id)/page'].videoInfoRes.item_list[0];

            let targetUrl = item.video.play_addr.url_list[0].replace(
              /\\u([\dA-F]{4})/gi,
              (match, grp) => {
                return String.fromCharCode(parseInt(grp, 16));
              }
            );

            let remoteCoverUrl = item.video.cover.url_list[0].replace(
              /\\u([\dA-F]{4})/gi,
              (match, grp) => {
                return String.fromCharCode(parseInt(grp, 16));
              }
            );

            const author = item?.author.nickname;
            const title = item?.desc;
            const videoDate = item?.create_time;

            await this.utils.downloadFile(
              targetUrl,
              filePath,
              `${fileName}${extMp4}`,
              ''
            );

            await this.utils.randomSleep(3);

            await this.utils.downloadFile(
              remoteCoverUrl,
              filePath,
              `${fileName}${extJpeg}`,
              ''
            );

            await this.douyinUrlEntity.update(
              {
                id: url.id,
              },
              {
                title,
                author,
                videoDate,
                type,
                localFile: `/upload/douyin/${yearMonthDate}${fileName}${extMp4}`,
                targetUrl,
                remoteCoverUrl,
                status: 1,
                run: 0,
              }
            );
          }
        } else {
          await this.utils.downloadFile(
            url.targetUrl,
            filePath,
            `${fileName}${extMp4}`,
            cookie
          );

          await this.utils.randomSleep(3);

          await this.utils.downloadFile(
            url.remoteCoverUrl,
            filePath,
            `${fileName}${extJpeg}`,
            cookie
          );

          await this.douyinUrlEntity.update(
            {
              id: url.id,
            },
            {
              type,
              localFile: `/upload/douyin/${yearMonthDate}${fileName}${extMp4}`,
              status: 1,
              run: 0,
            }
          );
        }

        // const rawOss = `/tingwu/raw/${yearMonthDate}${fileName}${extMp4}`;
        // const ossResult = await this.ossService.put(
        //   rawOss,
        //   `${filePath}${fileName}${extMp4}`
        // );

        // let config = new $OpenApi.Config({
        //   accessKeyId: this.oss.client.accessKeyId,
        //   accessKeySecret: this.oss.client.accessKeySecret,
        //   endpoint: this.tingwu.endpoint,
        // });

        // const client = new tingwu20220930(config);
        // let runtime = new $Util.RuntimeOptions({});
        // let headers: { [key: string]: string } = {};

        // let createFileTransRequest = new $tingwu20220930.CreateFileTransRequest(
        //   {
        //     audioOssPath: rawOss.substring(1),
        //     audioLanguage: this.tingwu.audioLanguage,
        //     audioOssBucket: this.oss.client.bucket,
        //     transResultOssBucket: this.oss.client.bucket,
        //     transResultOssPath: `tingwu/dist/${yearMonthDate}${fileName}${extTxt}`,
        //     appKey: this.tingwu.appKey,
        //   }
        // );

        // const res = await client.createFileTransWithOptions(
        //   createFileTransRequest,
        //   headers,
        //   runtime
        // );

        // if (res.body.code == '0') {
        //   const transId = res.body.data.transId;
        //   await this.douyinUrlEntity.update(
        //     {
        //       id: url.id,
        //     },
        //     {
        //       targetUrl: url.targetUrl,
        //       localFile: `/upload/douyin/${yearMonthDate}${fileName}${extMp4}`,
        //       remoteFilePath: `tingwu/dist/${yearMonthDate}${fileName}${extTxt}`,
        //       remoteFileUrl: ossResult.url,
        //       transId,
        //       status: 1,
        //     }
        //   );
        // } else {
        //   const message = res.body.message;
        //   await this.douyinUrlEntity.update(
        //     {
        //       id: url.id,
        //     },
        //     {
        //       targetUrl: url.targetUrl,
        //       localFile: `/upload/douyin/${yearMonthDate}${fileName}${extMp4}`,
        //       remoteFilePath: `tingwu/dist/${yearMonthDate}${fileName}${extTxt}`,
        //       remoteFileUrl: ossResult.url,
        //       status: -1,
        //       message,
        //     }
        //   );
        // }

        if (url.followId) {
          const total = await this.douyinUrlEntity.find({
            where: {
              followId: url.followId,
              status: MoreThanOrEqual(-2),
            },
          });

          const info = await this.douyinUrlEntity.findOne({
            where: {
              followId: url.followId,
              status: 1,
            },
            order: {
              videoDate: 'DESC',
            },
          });

          const finish = total.filter(
            row => row.status === 1 || row.status === -2
          );

          await this.douyinFollowEntity.update(
            {
              id: url.followId,
            },
            {
              lastVideoDate: info?.videoDate || 0,
              total: total.length || 0,
              finish: finish.length || 0,
              message: '',
            }
          );
        }

        await this.utils.randomSleep(30);
      } catch (err) {
        await this.douyinUrlEntity.update(
          {
            id: url.id,
          },
          {
            status: -2,
            run: 0,
            message: err.message,
          }
        );
        this.logger.error(err.message);
      }
    }

    await this.midwayCache.del('douyinWork');
  }

  async parseXiguaUrl(url) {
    const ttWid = await this.get_tt_wid();

    const res = await axios.get(url, {
      headers: {
        'User-Agent': this.douyin.agent,
        Cookie: ttWid,
      },
      maxRedirects: 0,
      validateStatus: status => {
        return status >= 200 && status < 400;
      },
    });

    return res.data;
  }

  async get_tt_wid() {
    const headers = {
      'User-Agent': this.douyin.agent,
    };
    const api = 'https://ttwid.bytedance.com/ttwid/union/register/';
    const data = {
      region: 'cn',
      aid: 1768,
      needFid: false,
      service: 'www.ixigua.com',
      migrate_info: {
        ticket: '',
        source: 'node',
      },
      cbUrlProtocol: 'https',
      union: true,
    };

    try {
      const response = await axios.post(api, data, {
        headers: headers,
        timeout: 10000,
      });

      const s = response.headers['set-cookie'];
      return s[0].split(';', 1)[0];
    } catch (error) {
      throw new Error('提取 ttwid 参数失败！');
    }
  }
}

/**
 * 抖音追踪最新视频定时任务
 */
@Job({
  cronTime: FORMAT.CRONTAB.EVERY_MINUTE,
  start: true,
})
export class FollowUpJob implements IJob {
  @InjectEntityModel(DouyinUrlEntity)
  douyinUrlEntity: Repository<DouyinUrlEntity>;

  @InjectEntityModel(DouyinAccountEntity)
  douyinAccountEntity: Repository<DouyinAccountEntity>;

  @InjectEntityModel(DouyinFollowEntity)
  douyinFollowEntity: Repository<DouyinFollowEntity>;

  @Inject()
  baseSysConfService: BaseSysConfService;

  @Inject()
  utils: Utils;

  @Logger()
  logger: ILogger;

  @InjectClient(CachingFactory, 'default')
  midwayCache: MidwayCache;

  @Inject()
  baseDir;

  @Config('douyin')
  douyin;

  async onTick() {
    let work = (await this.midwayCache.get('douyinFollowUp')) || 0;

    if (work === 1) return;

    await this.midwayCache.set('douyinFollowUp', 1, 1000 * 60 * 5);

    const follows =
      (await this.douyinFollowEntity.find({
        where: {
          status: 0,
          after: 1,
        },
        order: {
          lastRunAt: 'ASC',
        },
      })) || [];

    const webid = await this.utils.get_webid();
    const msToken = await this.utils.get_msToken();
    const verifyFp = this.utils.get_verify_fp();

    for (const follow of follows) {
      const accounts = await this.douyinAccountEntity.find();
      const account = _.sample(accounts);
      const cookie = account.cookie;

      if (follow.lastRunAt + follow.interval < this.utils.now()) {
        try {
          let addArr = [];
          let i = 0;
          let max_cursor = 0;

          while (true) {
            const arr = [];

            const query = `device_platform=webapp&aid=6383&channel=channel_pc_web&sec_user_id=${follow.dyUid}&max_cursor=${max_cursor}&show_live_replay_strategy=1&need_time_list=1&time_list_query=0&whale_cut_token=&cut_version=1&count=35&publish_video_strategy_type=2&update_version_code=170400&pc_client_type=1&version_code=290100&version_name=29.1.0&cookie_enabled=true&screen_width=2560&screen_height=1440&browser_language=zh-CN&browser_platform=MacIntel&browser_name=Chrome&browser_version=*********&browser_online=true&engine_name=Blink&engine_version=*********&os_name=Mac+OS&os_version=10.15.7&cpu_core_num=6&device_memory=8&platform=PC&downlink=10&effective_type=4g&round_trip_time=50&webid=${webid}&msToken=${msToken}`;
            const a_bogus = generate_a_bogus(query, this.douyin.agent);
            const url = `https://www.douyin.com/aweme/v1/web/aweme/post/?${query}&a_bogus=${a_bogus}&verifyFp=${verifyFp}&fp=${verifyFp}`;

            const home = await this.utils.douyinHomeInfo(url, cookie);
            max_cursor = home.max_cursor;
            home.aweme_list = home.aweme_list || [];
            home.has_more = home.has_more || 0;

            if (home.has_more === 0 && i > 0) break;

            for (const info of home.aweme_list) {
              if (follow.avatar.length === 0) {
                try {
                  let avatar = info.author.avatar_thumb.url_list.find(
                    (el: string) => el.includes('.jpeg')
                  );

                  const yearMonthDate = this.utils.yearMonthDate();
                  const filePath = `${this.baseDir}/../upload/douyin/${yearMonthDate}`;
                  const fileName = `${this.utils.today('YYYYMMDDHHmmss')}`;
                  const extJpeg = '.jpeg';

                  await this.utils.downloadFile(
                    avatar,
                    filePath,
                    `${fileName}${extJpeg}`,
                    ''
                  );

                  avatar = `https://connect.chasedream.com/upload/douyin/${yearMonthDate}${fileName}${extJpeg}`;
                  await this.douyinFollowEntity.update(
                    {
                      id: follow.id,
                    },
                    {
                      avatar,
                    }
                  );
                } catch (err) {
                  this.logger.error(err.message);
                }
              }

              const cover = info.video.cover.url_list.find((el: string) =>
                el.includes('.jpeg')
              );

              arr.push({
                uid: follow.uid,
                url: `https://www.douyin.com/video/${info.aweme_id}`,
                title: info.desc,
                author: info.author.nickname,
                remoteCoverUrl: cover,
                followId: follow.id,
                run: 1,
              });
            }

            i++;
            let urls = arr.map(row => row.url);

            if (urls.length) {
              const existingUrls = await this.douyinUrlEntity
                .createQueryBuilder('douyin_url')
                .select('douyin_url.url')
                .where('douyin_url.uid = :uid', { uid: follow.uid })
                .andWhere('douyin_url.url IN (:...urls)', { urls })
                .getMany();

              if (existingUrls.length === urls.length) break;

              const existingUrlsSet = new Set(
                existingUrls.map(urlEntity => urlEntity.url)
              );

              const filtered = arr.filter(row => !existingUrlsSet.has(row.url));
              addArr = addArr.concat(filtered);
            }

            await this.utils.randomSleep(3);
          }

          try {
            if (addArr.length) await this.douyinUrlEntity.save(addArr);
          } catch (error) {
            this.logger.error(error.message);
          }

          await this.utils.randomSleep(10);
        } catch (err) {
          await this.douyinFollowEntity.update(
            {
              id: follow.id,
            },
            {
              lastRunAt: this.utils.now(),
              message: `followUp: ${err.message}`,
            }
          );

          this.logger.error(err.message);
        } finally {
          await this.douyinFollowEntity.update(
            {
              id: follow.id,
            },
            {
              lastRunAt: this.utils.now(),
            }
          );
        }
      }
    }

    await this.midwayCache.del('douyinFollowUp');
  }
}

/**
 * 初始化追踪定时任务
 */
@Job({
  cronTime: FORMAT.CRONTAB.EVERY_MINUTE,
  start: true,
})
export class InitFollowJob implements IJob {
  @InjectEntityModel(DouyinUrlEntity)
  douyinUrlEntity: Repository<DouyinUrlEntity>;

  @InjectEntityModel(DouyinAccountEntity)
  douyinAccountEntity: Repository<DouyinAccountEntity>;

  @InjectEntityModel(DouyinFollowEntity)
  douyinFollowEntity: Repository<DouyinFollowEntity>;

  @Inject()
  baseSysConfService: BaseSysConfService;

  @Inject()
  utils: Utils;

  @InjectClient(CachingFactory, 'default')
  midwayCache: MidwayCache;

  @Logger()
  logger: ILogger;

  @Inject()
  baseDir;

  @Config('douyin')
  douyin;

  async onTick() {
    let work = (await this.midwayCache.get('douyinInitFollow')) || 0;
    if (work === 1) return;

    await this.midwayCache.set('douyinInitFollow', 1, 1000 * 60 * 5);

    const follows =
      (await this.douyinFollowEntity.find({
        where: {
          status: -1,
          before: 1,
        },
      })) || [];

    const webid = await this.utils.get_webid();
    const msToken = await this.utils.get_msToken();
    const verifyFp = this.utils.get_verify_fp();

    for (const follow of follows) {
      const accounts = await this.douyinAccountEntity.find();
      const account = _.sample(accounts);
      const cookie = account.cookie;

      try {
        let addArr = [];
        let i = 0;
        let j = 0;
        let max_cursor = 0;
        let avatar = '';

        while (true) {
          const arr = [];

          const query = `device_platform=webapp&aid=6383&channel=channel_pc_web&sec_user_id=${follow.dyUid}&max_cursor=${max_cursor}&show_live_replay_strategy=1&need_time_list=1&time_list_query=0&whale_cut_token=&cut_version=1&count=35&publish_video_strategy_type=2&update_version_code=170400&pc_client_type=1&version_code=290100&version_name=29.1.0&cookie_enabled=true&screen_width=2560&screen_height=1440&browser_language=zh-CN&browser_platform=MacIntel&browser_name=Chrome&browser_version=*********&browser_online=true&engine_name=Blink&engine_version=*********&os_name=Mac+OS&os_version=10.15.7&cpu_core_num=6&device_memory=8&platform=PC&downlink=10&effective_type=4g&round_trip_time=50&webid=${webid}&msToken=${msToken}`;
          const a_bogus = generate_a_bogus(query, this.douyin.agent);
          const url = `https://www.douyin.com/aweme/v1/web/aweme/post/?${query}&a_bogus=${a_bogus}&verifyFp=${verifyFp}&fp=${verifyFp}`;

          const home = await this.utils.douyinHomeInfo(url, cookie);
          max_cursor = home.max_cursor;
          home.aweme_list = home.aweme_list || [];
          home.has_more = home.has_more || 0;

          if (home.has_more === 0 && i > 0) break;

          for (const info of home.aweme_list) {
            if (j === 0) {
              avatar = info.author.avatar_thumb.url_list.find((el: string) =>
                el.includes('.jpeg')
              );

              const yearMonthDate = this.utils.yearMonthDate();
              const filePath = `${this.baseDir}/../upload/douyin/${yearMonthDate}`;
              const fileName = `${this.utils.today('YYYYMMDDHHmmss')}`;
              const extJpeg = '.jpeg';

              await this.utils.downloadFile(
                avatar,
                filePath,
                `${fileName}${extJpeg}`,
                ''
              );

              avatar = `https://connect.chasedream.com/upload/douyin/${yearMonthDate}${fileName}${extJpeg}`;
            }

            const cover = info.video.cover.url_list.find((el: string) =>
              el.includes('.jpeg')
            );

            const targetUrl = info.video.play_addr.url_list.find((el: string) =>
              el.includes('v1/play')
            );

            arr.push({
              uid: follow.uid,
              url: `https://www.douyin.com/video/${info.aweme_id}`,
              targetUrl,
              title: info.desc,
              author: info.author.nickname,
              remoteCoverUrl: cover,
              followId: follow.id,
              type: info.aweme_type === 0 ? 'video' : 'note',
              run: j <= 2,
            });

            j++;
          }

          i++;
          let urls = arr.map(row => row.url);

          if (urls.length) {
            const existingUrls = await this.douyinUrlEntity
              .createQueryBuilder('douyin_url')
              .select('douyin_url.url')
              .where('douyin_url.uid = :uid', { uid: follow.uid })
              .andWhere('douyin_url.url IN (:...urls)', { urls })
              .getMany();

            const existingUrlsSet = new Set(
              existingUrls.map(urlEntity => urlEntity.url)
            );

            const filtered = arr.filter(row => !existingUrlsSet.has(row.url));
            addArr = addArr.concat(filtered);
          }

          await this.utils.randomSleep(5);
        }

        try {
          if (addArr.length) await this.douyinUrlEntity.save(addArr);
        } catch (error) {
          this.logger.error(error.message);
        }

        await this.douyinFollowEntity.update(
          {
            id: follow.id,
          },
          {
            avatar,
            status: 0,
            message: '',
          }
        );

        await this.utils.randomSleep(60);
      } catch (err) {
        await this.douyinFollowEntity.update(
          {
            id: follow.id,
          },
          {
            message: `initFollow: ${err.message}`,
          }
        );

        this.logger.error(err.message);
      }
    }

    await this.midwayCache.del('douyinInitFollow');
  }
}
