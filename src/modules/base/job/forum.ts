import { <PERSON>, <PERSON><PERSON><PERSON> } from '@midwayjs/cron';
import { IMidwayApplication } from '@midwayjs/core';
import { App, Logger, FORMAT, Config, Inject } from '@midwayjs/decorator';
import { BaseSysConfService } from '../service/sys/conf';
import { EmailService } from '../service/common/email';
import { InjectEntityModel } from '@midwayjs/typeorm';
import { In, MoreThanOrEqual, Repository } from 'typeorm';
import { CcForumPost } from '../entity/forum/CcForumPost';
import { CcCommonMember } from '../entity/forum/CcCommonMember';
import { CcCommonMemberCrime } from '../entity/forum/CcCommonMemberCrime';
import { CcCommonMemberProfile } from '../entity/forum/CcCommonMemberProfile';
import { CcCommonMemberFieldForum } from '../entity/forum/CcCommonMemberFieldForum';
import { CcHomeNotification } from '../entity/forum/CcHomeNotification';
import { ILogger } from '@midwayjs/logger';
import { Utils } from '../../../comm/utils';
import { CcCommonSearchRecommend } from '../entity/forum/CcCommonSearchRecommend';
import { CoolElasticSearch } from '@cool-midway/es';
import { CcForumThreadTrigger } from '../entity/forum/CcForumThreadTrigger';
import { CcForumThreadWeight } from '../entity/forum/CcForumThreadWeight';
import { CcForumPostTrigger } from '../entity/forum/CcForumPostTrigger';
import * as _ from 'lodash';
import { CcCommonMemberStatus } from '../entity/forum/CcCommonMemberStatus';
import { CommonForumService } from '../service/common/forum';
const moment = require('moment-timezone');

/**
 * 监控180天内相同IP注册用户
 */
@Job({
  cronTime: '0 0 0,8,16 * * *',
  start: true,
})
export class UserScanJob implements IJob {
  @App()
  app: IMidwayApplication;

  @InjectEntityModel(CcCommonMember, 'forum')
  ccCommonMember: Repository<CcCommonMember>;

  @InjectEntityModel(CcCommonMemberStatus, 'forum')
  ccCommonMemberStatus: Repository<CcCommonMemberStatus>;

  @InjectEntityModel(CcCommonMemberCrime, 'forum')
  ccCommonMemberCrime: Repository<CcCommonMemberCrime>;

  @Inject()
  emailService: EmailService;

  @Inject()
  utils: Utils;

  @Config('noticeType')
  noticeType;

  async onTick() {
    const arr = [];
    const hour = moment().hour();
    const todayTS =
      hour >= 0 && hour < 1 ? this.utils.subDaysTS(1) : this.utils.todayTS();

    const members = await this.ccCommonMember
      .createQueryBuilder('member')
      .where('regdate >= :regdate and status <> -1', {
        regdate: todayTS,
      })
      .getMany();

    for (const member of members) {
      try {
        const ms = await this.ccCommonMemberStatus.findOneBy({
          uid: member.uid,
        });

        const uids = (
          await this.ccCommonMemberStatus.find({
            where: [{ regip: ms.regip }, { lastip: ms.regip }],
          })
        ).map(row => row.uid);

        const ts = this.utils.subDaysTS(180);
        const count = await this.ccCommonMemberCrime.countBy({
          uid: In(uids),
          dateline: MoreThanOrEqual(ts),
        });

        if (count > 0) {
          arr.push(member);

          await this.ccCommonMember.update(
            {
              uid: member.uid,
            },
            {
              status: -1,
            }
          );

          await this.ccCommonMemberCrime.save({
            uid: member.uid,
            operatorid: 637,
            operator: 'ChaseDream',
            action: 6,
            reason: '180天内相同IP注册且有处罚记录',
            dateline: this.utils.now(),
          });
        }
      } catch (err) {
        console.log(err.message);
      }
    }

    if (arr.length) {
      const message = arr
        .map(row => {
          return `UID：${row.uid}<br />昵称：<a href='https://forum.chasedream.com/?${row.uid}' target='_blank'>${row.username}</a><br /><br />`;
        })
        .join('');

      await this.emailService.sendmail({
        toemail: this.noticeType.email,
        subject: '论坛发现可疑用户注册',
        message,
      });
    }
  }
}

/**
 * 监控帖子敏感词
 */
@Job({
  cronTime: '0 0 3 * * *',
  start: true,
})
export class NoticeSensitiveInfoPostJob implements IJob {
  @App()
  app: IMidwayApplication;

  @InjectEntityModel(CcForumPost, 'forum')
  ccForumPost: Repository<CcForumPost>;

  @Inject()
  baseSysConfService: BaseSysConfService;

  @Inject()
  emailService: EmailService;

  async onTick() {
    let result = '';

    let sens: any =
      (await this.baseSysConfService.getValue('forumSensitive')) || '';
    sens = sens.split('\n');

    let posts = await this.ccForumPost
      .createQueryBuilder('post')
      .where(
        sens
          .map((keyword, index) => `post.message LIKE :keyword${index}`)
          .join(' OR '),
        sens.reduce(
          (acc, keyword, index) => ({
            ...acc,
            [`keyword${index}`]: `%${keyword}%`,
          }),
          {}
        )
      )
      .getMany();

    for (const post of posts) {
      const url = `https://forum.chasedream.com/forum.php?mod=redirect&goto=findpost&ptid=${post.tid}&pid=${post.pid}`;
      let found = sens.filter(el => post?.message.includes(el));

      result += `UID：${post.authorid}<br />帖子：<a href='${url}' target='_blank'>帖子链接</a><br />命中：${found[0]}<br /><br />`;
    }

    if (result.length) {
      await this.emailService.sendmail({
        toemail:
          this.app.getEnv() === 'prod'
            ? '<EMAIL>'
            : '<EMAIL>',
        subject: '论坛发现可疑帖子',
        message: result,
      });
    }
  }
}

/**
 * 监控个人信息敏感词
 */
@Job({
  cronTime: '0 0 */2 * * *',
  start: true,
})
export class NoticeSensitiveInfoJob implements IJob {
  @App()
  app: IMidwayApplication;

  @InjectEntityModel(CcForumPost, 'forum')
  ccForumPost: Repository<CcForumPost>;

  @InjectEntityModel(CcCommonMember, 'forum')
  ccCommonMember: Repository<CcCommonMember>;

  @InjectEntityModel(CcCommonMemberCrime, 'forum')
  ccCommonMemberCrime: Repository<CcCommonMemberCrime>;

  @InjectEntityModel(CcCommonMemberProfile, 'forum')
  ccCommonMemberProfile: Repository<CcCommonMemberProfile>;

  @InjectEntityModel(CcCommonMemberFieldForum, 'forum')
  ccCommonMemberFieldForum: Repository<CcCommonMemberFieldForum>;

  @InjectEntityModel(CcHomeNotification, 'forum')
  ccHomeNotification: Repository<CcHomeNotification>;

  @Inject()
  baseSysConfService: BaseSysConfService;

  @Inject()
  emailService: EmailService;

  @Logger()
  logger: ILogger;

  @Inject()
  utils: Utils;

  async onTick() {
    let result = '';
    const entityManager = this.ccCommonMember.manager;

    let sens: any =
      (await this.baseSysConfService.getValue('forumSensitive')) || '';
    sens = sens.split('\n');

    let uidW = sens
      .map(el => {
        return [`a.bio like '%${el}%'`, `b.sightml like '%${el}%'`];
      })
      .flat()
      .join(' or ');

    let uid =
      await entityManager.query(`SELECT a.uid from cc_common_member_profile a
      left join cc_common_member_field_forum b on a.uid=b.uid
			left join cc_common_member c on a.uid=c.uid
       WHERE (${uidW}) or (c.email like '%@yopmail.fr' and c.status<> -1)`);

    uid = uid.map(el => el.uid);

    let authoridW = sens
      .map(el => {
        return `note like '%${el}%'`;
      })
      .join(' or ');

    let authorid = await entityManager.query(
      `SELECT DISTINCT authorid from cc_home_notification where (${authoridW}) and type='friend'`
    );
    authorid = authorid.map(el => el.authorid);

    const uids = [...new Set([...uid, ...authorid])];

    if (uids.length) {
      const members = await this.ccCommonMember.findBy({
        uid: In(uids),
      });

      for (const member of members) {
        if (member.status === 0) {
          try {
            await this.ccCommonMember.update(
              {
                uid: member.uid,
              },
              {
                status: -1,
              }
            );

            let reason = '';

            if (uid && uid.length > 0 && uid.includes(member.uid)) {
              const mp = await this.ccCommonMemberProfile.findOne({
                where: {
                  uid: member.uid,
                },
              });

              const mff = await this.ccCommonMemberFieldForum.findOne({
                where: {
                  uid: member.uid,
                },
              });

              let found = sens.filter(
                el => mp?.bio.includes(el) || mff?.sightml.includes(el)
              );

              reason = `个人资料 ${found.join(' ')} ${mp?.bio} ${mff?.sightml}`;
            } else {
              const queryBuilder =
                await this.ccHomeNotification.createQueryBuilder('');

              queryBuilder.where('authorid = :authorid', {
                authorid: member.uid,
              });

              queryBuilder.andWhere(authoridW);

              const hn = await queryBuilder.getOne();

              let found = sens.filter(el => hn?.note.includes(el));

              reason = `站内短信 ${found.join(' ')} ${hn?.note}`;
            }

            await this.ccCommonMemberCrime.save({
              uid: member.uid,
              operatorid: 637,
              operator: 'ChaseDream',
              action: 6,
              reason,
              dateline: this.utils.now(),
            });

            await this.utils.delDZAvatar(member.uid);
          } catch (err) {
            this.logger.info(err.message);
          }

          result += `UID：${member.uid}<br />昵称：<a href='https://forum.chasedream.com/?${member.uid}' target='_blank'>${member.username}</a><br />`;
        }
      }

      if (result.length) {
        await this.emailService.sendmail({
          toemail:
            this.app.getEnv() === 'prod'
              ? '<EMAIL>'
              : '<EMAIL>',
          subject: '论坛发现可疑用户',
          message: result,
        });
      }
    }
  }
}

/**
 * 敏感信息清理
 */
@Job({
  cronTime: FORMAT.CRONTAB.EVERY_DAY_ONE_FIFTEEN,
  start: true,
})
export class SensitiveInfoClearJob implements IJob {
  @InjectEntityModel(CcForumPost, 'forum')
  ccForumPost: Repository<CcForumPost>;

  async onTick() {
    const entityManager = this.ccForumPost.manager;

    let uid = await entityManager.query(
      "DELETE from cc_home_notification where note like '%禁闻%'"
    );
  }
}

/**
 * 搜索推荐清理
 */
@Job({
  cronTime: FORMAT.CRONTAB.EVERY_MINUTE,
  start: true,
})
export class SearchRecommendClearJob implements IJob {
  @InjectEntityModel(CcCommonSearchRecommend, 'forum')
  ccCommonSearchRecommend: Repository<CcCommonSearchRecommend>;

  @Inject()
  es: CoolElasticSearch;

  @Inject()
  utils: Utils;

  async onTick() {
    const els = await this.ccCommonSearchRecommend
      .createQueryBuilder('')
      .where('status=1')
      .getMany();

    const now = this.utils.now();

    for (let el of els) {
      if (el.end < now && el.end !== 0) {
        await this.ccCommonSearchRecommend.update(el.id, {
          status: false,
        });

        await this.es.client.update({
          index: 'search_recommand',
          id: el.id,
          doc: {
            status: false,
          },
        });
      }
    }

    await this.es.client.indices.refresh({ index: 'search_recommand' });
  }
}

/**
 * 更新论坛主题索引
 */
@Job({
  cronTime: FORMAT.CRONTAB.EVERY_PER_10_SECOND,
  start: true,
})
export class UpdateForumThreadIndexJob implements IJob {
  @InjectEntityModel(CcForumThreadWeight, 'forum')
  ccForumThreadWeight: Repository<CcForumThreadWeight>;

  @InjectEntityModel(CcForumThreadTrigger, 'forum')
  CcForumThreadTrigger: Repository<CcForumThreadTrigger>;

  @Inject()
  es: CoolElasticSearch;

  @Config('discuz')
  discuz;

  @Inject()
  utils: Utils;

  async onTick() {
    const index = 'forum_post';
    const entityManager = this.ccForumThreadWeight.manager;

    const els = await this.CcForumThreadTrigger.createQueryBuilder('')
      .take(1000)
      .getMany();

    if (els.length === 0) return;

    this.es.client.deleteByQuery({
      index,
      refresh: true,
      body: {
        query: {
          terms: { tid: els.map(el => el.tid) },
        },
      },
    });

    const tids = _.filter(els, { type: 'update' })?.map(el => {
      if (!this.discuz.limitFid.includes(el.fid)) return el.tid;
    });

    if (tids.length !== 0) {
      const posts = await entityManager.query(
        `SELECT a.fid, a.pid, a.tid, a.message, b.subject, b.digest, b.displayorder, b.lastpost FROM forum.cc_forum_post a
              LEFT OUTER JOIN forum.cc_forum_thread b
              on a.tid = b.tid
              where a.tid in (?)`,
        [tids]
      );

      const tws = await this.ccForumThreadWeight
        .createQueryBuilder()
        .where('tid in (:tids)', { tids })
        .getMany();
      const twsMap = new Map(
        tws.map((el: any) => {
          return [el.tid, el.weight];
        })
      );

      const operations = posts.flatMap(doc => {
        const tw = twsMap.get(doc.tid);
        doc.weight = tw ? tw : 0;

        return [{ index: { _index: index, _id: doc.pid } }, doc];
      });

      if (operations.length) {
        await this.es.client.bulk({ operations, refresh: true });
      }
    }

    await this.CcForumThreadTrigger.delete(els.map(el => el.id));
  }
}

/**
 * 更新论坛帖子索引
 */
@Job({
  cronTime: FORMAT.CRONTAB.EVERY_PER_10_SECOND,
  start: true,
})
export class UpdateForumPostIndexJob implements IJob {
  @InjectEntityModel(CcForumPostTrigger, 'forum')
  CcForumPostTrigger: Repository<CcForumPostTrigger>;

  @Inject()
  commonForumService: CommonForumService;
  
  @Inject()
  es: CoolElasticSearch;

  @Config('discuz')
  discuz;

  @Inject()
  utils: Utils;

  async onTick() {
    const index = 'forum_post';
    const entityManager = this.CcForumPostTrigger.manager;

    const els = await this.CcForumPostTrigger.createQueryBuilder('')
      .take(1000)
      .getMany();

    if (els.length === 0) return;

    this.es.client.deleteByQuery({
      index,
      refresh: true,
      body: {
        query: {
          terms: { pid: els.map(el => el.pid) },
        },
      },
    });

    const pids = els
      .filter(el => {
        return el.type !== 'delete';
      })
      ?.map(el => {
        if (!this.discuz.limitFid.includes(el.fid)) return el.pid;
      });

    if (pids.length !== 0) {
      const posts = await entityManager.query(
        `SELECT a.fid, a.pid, a.tid, a.message, b.subject, b.digest, b.displayorder, b.lastpost FROM forum.cc_forum_post a
              LEFT OUTER JOIN forum.cc_forum_thread b
              on a.tid = b.tid
              where a.pid in (?)`,
        pids
      );

      const operations = posts.flatMap(doc => {
        doc.weight = 0;
        return [{ index: { _index: index, _id: doc.pid } }, doc];
      });

      if (operations.length) {
        await this.es.client.bulk({ operations, refresh: true });
      }
      
      try {
        await this.commonForumService.scanPostsByPids({ pids });
      } catch (error) {
        console.log(error.message);
      }
    }

    await this.CcForumPostTrigger.delete(els.map(el => el.id));
  }
}

/**
 * 初始化论坛索引数据
 */
@Job({
  cronTime: FORMAT.CRONTAB.EVERY_PER_10_SECOND,
  start: true,
})
export class InitForumIndexDataJob implements IJob {
  @InjectEntityModel(CcForumPostTrigger, 'forum')
  CcForumPostTrigger: Repository<CcForumPostTrigger>;

  @Inject()
  baseSysConfService: BaseSysConfService;

  @Inject()
  es: CoolElasticSearch;

  @Config('discuz')
  discuz;

  @Logger()
  logger: ILogger;

  @Inject()
  utils: Utils;

  async onTick() {
    const index = 'forum_post';
    const entityManager = this.CcForumPostTrigger.manager;
    const limitFid = this.discuz.limitFid;

    let maxPid = (await this.baseSysConfService.getValue('maxPid')) || 0;

    let total = await entityManager.query(
      'SELECT count(*) count FROM forum.cc_forum_post where pid > ? and fid not in(?)',
      [maxPid, limitFid]
    );

    total = Number(total[0].count);
    let page = 0;
    let pageSize = 1000;

    while (true) {
      const posts = await entityManager.query(
        `SELECT a.fid, a.pid, a.tid, a.message, b.subject, b.digest, b.displayorder, b.lastpost FROM forum.cc_forum_post a
              LEFT OUTER JOIN forum.cc_forum_thread b
              on a.tid = b.tid
              where a.pid > ? and a.fid not in(?)
              order by a.pid ASC
              limit ${pageSize}`,
        [maxPid, limitFid]
      );

      try {
        const operations = posts.flatMap(doc => {
          doc.weight = 0;
          return [{ index: { _index: index, _id: doc.pid } }, doc];
        });

        if (posts.length) {
          const res = await this.es.client.bulk({ operations });

          if (res.errors) {
            this.logger.error(JSON.stringify(res.items));
          }
        }
      } catch (err) {
        console.log(err.message);
      } finally {
        if (posts?.length) {
          const pids = posts.map(el => el.pid);
          maxPid = Math.max(...pids);

          await this.baseSysConfService.updateVaule('maxPid', maxPid);
        }
      }

      if ((page === 0 ? 1 : page + 1) * pageSize > total) break;
      else page++;
    }
  }
}
