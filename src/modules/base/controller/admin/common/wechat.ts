import {
  Provide,
  Post,
  Inject,
  Body,
  ALL,
  WSController,
  OnWSConnection,
  OnWSMessage,
} from '@midwayjs/decorator';
import { CoolController, BaseController } from '@cool-midway/core';
import { WechatGroupEntity } from '../../../entity/wx/wechatGroup';
import { WechatService } from '../../../service/common/wechat';
import { Context } from '@midwayjs/socketio';
import { BaseSysConfService } from '../../../service/sys/conf';

/**
 * Wechat Bot
 */
@Provide()
@WSController('/wx/wechat')
@CoolController({
  api: ['info'],
  entity: WechatGroupEntity,
})
export class CommonWechatController extends BaseController {
  @Inject()
  ctx: Context;

  @Inject()
  baseSysConfService: BaseSysConfService;

  @Inject()
  wechatService: WechatService;

  @Post('/getQrCode', { summary: '获取登录二维码' })
  async getQrCode(@Body(ALL) param: any) {
    const qrCode = await this.baseSysConfService.getValue('wechatQrCode');
    return this.ok({ qrCode });
  }

  @Post('/wechatInfo', { summary: '微信信息' })
  async wechatInfo(@Body(ALL) param: any) {
    return this.ok({ wechatInfo: global.wechatInfo });
  }

  @Post('/logout', { summary: '退出登录' })
  async logout(@Body(ALL) param: any) {
    await global.wechatBot.logout();
    return this.ok();
  }

  @Post('/list', { summary: '聊天列表' })
  async groupList(@Body(ALL) param: any) {
    return this.ok(await this.wechatService.groupList(param));
  }

  @Post('/messages', { summary: '消息列表' })
  async messages(@Body(ALL) param: any) {
    return this.ok(await this.wechatService.messages(param));
  }

  @Post('/updateGroup', { summary: '编辑组信息' })
  async updateGroup(@Body(ALL) param: any) {
    return this.ok(await this.wechatService.updateGroup(param));
  }

  @OnWSConnection()
  async onConnectionMethod() {
    this.ctx.emit('sys', '连接成功');
  }

  @OnWSMessage('message')
  async gotMessage(param) {
    await this.wechatService.message(param);
  }
}
