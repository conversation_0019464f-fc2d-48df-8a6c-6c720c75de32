import { Provide, Inject, Post, Body, ALL } from '@midwayjs/decorator';
import { CoolController, BaseController } from '@cool-midway/core';
import { IESEService } from '../../../service/common/iese';
import { BlogEntity } from '../../../entity/iese/blog';

/**
 * IESE
 */
@Provide()
@CoolController({
  api: ['info'],
  entity: BlogEntity,
  service: IESEService,
})
export class CommonIESEController extends BaseController {
  @Inject()
  ieseService: IESEService;

  @Post('/findOne', { summary: '查找指定数据' })
  async findOne(@Body(ALL) param: any) {
    return this.ok(await this.ieseService.findOne(param));
  }

  @Post('/create', { summary: '创建' })
  async create(@Body(ALL) param: any) {
    return this.ok(await this.ieseService.create(param));
  }

  @Post('/edit', { summary: '编辑' })
  async edit(@Body(ALL) param: any) {
    return this.ok(await this.ieseService.edit(param));
  }

  @Post('/del', { summary: '删除' })
  async del(@Body(ALL) param: any) {
    return this.ok(await this.ieseService.del(param));
  }

  @Post('/pages', { summary: '列表' })
  async pages(@Body(ALL) param: any) {
    return this.ok(await this.ieseService.pages(param));
  }

  @Post('/tags', { summary: '类型列表' })
  async tags(@Body(ALL) param: any) {
    return this.ok(await this.ieseService.tags(param));
  }
}
