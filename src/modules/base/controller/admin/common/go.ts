import { Provide, Post, Inject, Body, ALL, Get } from '@midwayjs/decorator';
import { CoolController, BaseController } from '@cool-midway/core';
import { Url } from '../../../entity/go/Url';
import { GoService } from '../../../service/common/go';

/**
 * Go
 */
@Provide()
@CoolController({
  api: ['info'],
  entity: Url,
  service: GoService,
})
export class CommonForumController extends BaseController {
  @Inject()
  goService: GoService;

  @Get('/urlPage', { summary: 'Url列表' })
  async urlPage() {
    return this.ok(await this.goService.urlPage());
  }

  @Post('/urlByID', { summary: '根据ID获取Url' })
  async urlByID(@Body(ALL) param: any) {
    return this.ok(await this.goService.urlByID(param));
  }

  @Post('/urlSub', { summary: '获取Url子节点' })
  async urlSub(@Body(ALL) param: any) {
    return this.ok(await this.goService.urlSub(param));
  }

  @Post('/urlAdd', { summary: 'Url添加' })
  async urlAdd(@Body(ALL) param: any) {
    return this.ok(await this.goService.urlAdd(param));
  }

  @Post('/urlUpdate', { summary: 'Url更新' })
  async urlUpdate(@Body(ALL) param: any) {
    return this.ok(await this.goService.urlUpdate(param));
  }

  @Post('/urlDelete', { summary: 'Url删除' })
  async urlDelete(@Body(ALL) param: any) {
    return this.ok(await this.goService.urlDelete(param));
  }

  @Post('/update', { summary: 'Url状态更新' })
  async switchEnable(@Body(ALL) param: any) {
    return this.ok(await this.goService.switchEnable(param));
  }

  @Post('/statisticByUrl', { summary: '获取Statistic' })
  async statisticByUrl(@Body(ALL) param: any) {
    return this.ok(await this.goService.statisticByUrl(param));
  }

  @Post('/urlStatisticsDel', { summary: '删除Statistic' })
  async urlStatisticsDel(@Body(ALL) param: any) {
    return this.ok(await this.goService.urlStatisticsDel(param));
  }

  @Post('/detailByUrl', { summary: '获取Statistic' })
  async detailByUrl(@Body(ALL) param: any) {
    return this.ok(await this.goService.detailByUrl(param));
  }

  @Post('/queryGroup', { summary: '获取QueryGroup' })
  async queryGroup(@Body(ALL) param: any) {
    return this.ok(await this.goService.queryGroup(param));
  }

  @Post('/export', { summary: '导出Excel' })
  async export(@Body(ALL) param: any) {
    return this.ok(await this.goService.export(param));
  }
}
