import { Provide, Post, Inject, Body, ALL, Get } from '@midwayjs/decorator';
import { CoolController, BaseController } from '@cool-midway/core';
import { DouyinService } from '../../../service/common/douyin';
import { DouyinUrlEntity } from '../../../entity/douyin/douyinUrl';

/**
 * DouYin
 */
@Provide()
@CoolController({
  api: ['info'],
  entity: DouyinUrlEntity,
  service: DouyinService,
})
export class CommonDouyinController extends BaseController {
  @Inject()
  douyinService: DouyinService;

  @Post('/tagUserList', { summary: 'TagUser列表' })
  async tagUserList(@Body(ALL) param: any) {
    return this.ok(await this.douyinService.tagUserList(param));
  }

  @Post('/tagUserAll', { summary: '追踪用户对应的标签' })
  async tagUserAll(@Body(ALL) param: any) {
    return this.ok(await this.douyinService.tagUserAll(param));
  }

  @Post('/tagUserCreate', { summary: '创建TagUser' })
  async tagUserCreate(@Body(ALL) param: any) {
    return this.ok(await this.douyinService.tagUserCreate(param));
  }

  @Post('/tagUserDelete', { summary: '删除TagUser' })
  async tagUserDelete(@Body(ALL) param: any) {
    return this.ok(await this.douyinService.tagUserDelete(param));
  }

  @Post('/tagPage', { summary: 'Tag列表' })
  async tagPage(@Body(ALL) param: any) {
    return this.ok(await this.douyinService.tagPage(param));
  }

  @Post('/tagCreate', { summary: '创建Tag' })
  async tagCreate(@Body(ALL) param: any) {
    return this.ok(await this.douyinService.tagCreate(param));
  }

  @Post('/tagUpdate', { summary: '修改Tag' })
  async tagUpdate(@Body(ALL) param: any) {
    return this.ok(await this.douyinService.tagUpdate(param));
  }

  @Post('/tagDelete', { summary: '删除Tag' })
  async tagDelete(@Body(ALL) param: any) {
    return this.ok(await this.douyinService.tagDelete(param));
  }

  @Post('/urlPage', { summary: 'Url列表' })
  async urlPage(@Body(ALL) param: any) {
    return this.ok(await this.douyinService.urlPage(param));
  }

  @Post('/urlCreate', { summary: '创建Url' })
  async urlCreate(@Body(ALL) param: any) {
    return this.ok(await this.douyinService.urlCreate(param));
  }

  @Post('/urlUpdate', { summary: '修改Url' })
  async urlUpdate(@Body(ALL) param: any) {
    return this.ok(await this.douyinService.urlUpdate(param));
  }

  @Post('/urlImages', { summary: '获取Url对应图片' })
  async urlImages(@Body(ALL) param: any) {
    return this.ok(await this.douyinService.urlImages(param));
  }

  @Post('/userPage', { summary: '用户主页列表' })
  async userPage(@Body(ALL) param: any) {
    return this.ok(await this.douyinService.userPage(param));
  }

  @Post('/userUpdate', { summary: '更新用户主页' })
  async userUpdate(@Body(ALL) param: any) {
    return this.ok(await this.douyinService.userUpdate(param));
  }

  @Post('/homeCreate', { summary: 'Home创建' })
  async followUp(@Body(ALL) param: any) {
    return this.ok(await this.douyinService.homeCreate(param));
  }

  @Post('/douyinHome', { summary: '抖音主页' })
  async douyinHome(@Body(ALL) param: any) {
    return this.ok(await this.douyinService.douyinHome(param));
  }

  @Post('/douyinUrlParse', { summary: '抖音视频解析' })
  async douyinUrlParse(@Body(ALL) param: any) {
    return this.ok(await this.douyinService.douyinUrlParse(param));
  }

  @Post('/douyinUrlDownload', { summary: '抖音下载' })
  async douyinUrlDownload(@Body(ALL) param: any) {
    return this.ok(await this.douyinService.douyinUrlDownload(param));
  }

  @Post('/urlDelete', { summary: '删除Url' })
  async urlDelete(@Body(ALL) param: any) {
    return this.ok(await this.douyinService.urlDelete(param));
  }

  @Post('/urlRedo', { summary: '重新处理Url' })
  async urlRedo(@Body(ALL) param: any) {
    return this.ok(await this.douyinService.urlRedo(param));
  }

  @Get('/qrCodeUrl', { summary: 'QrCode Url' })
  async qrCodeUrl() {
    return this.ok(await this.douyinService.qrCodeUrl());
  }

  @Post('/checkQrCode', { summary: '检查QrCode' })
  async checkQrCode(@Body(ALL) param: any) {
    return this.ok(await this.douyinService.checkQrCode(param));
  }

  @Post('/loginQrCode', { summary: '用QrCode登录' })
  async loginQrCode(@Body(ALL) param: any) {
    return this.ok(await this.douyinService.loginQrCode(param));
  }

  @Get('/account', { summary: '账号列表' })
  async account() {
    return this.ok(await this.douyinService.account());
  }

  @Post('/accountCookie', { summary: '账号Cookie' })
  async accountCookie(@Body(ALL) param: any) {
    return this.ok(await this.douyinService.accountCookie(param));
  }

  @Post('/accountCreate', { summary: '创建账号' })
  async accountCreate(@Body(ALL) param: any) {
    return this.ok(await this.douyinService.accountCreate(param));
  }

  @Post('/accountDelete', { summary: '删除账号' })
  async accountDelete(@Body(ALL) param: any) {
    return this.ok(await this.douyinService.accountDelete(param));
  }

  @Post('/accountSwitch', { summary: '主账号切换' })
  async accountSwitch(@Body(ALL) param: any) {
    return this.ok(await this.douyinService.accountSwitch(param));
  }

  @Post('/accountStatus', { summary: '账号使能' })
  async accountStatus(@Body(ALL) param: any) {
    return this.ok(await this.douyinService.accountStatus(param));
  }
}
