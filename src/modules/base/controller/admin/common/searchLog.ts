import { CcCommonSearchLog } from './../../../entity/forum/CcCommonSearchLog';
import { CommonSearchLogService } from '../../../service/common/searchLog';
import { Provide, Inject } from '@midwayjs/decorator';
import { CoolController, BaseController } from '@cool-midway/core';

/**
 * 论坛搜索日志
 */
@Provide()
@CoolController({
  api: ['add', 'delete', 'update', 'info', 'list', 'page'],
  entity: CcCommonSearchLog,
  service: CommonSearchLogService,
})
export class CommonSougouController extends BaseController {
  @Inject()
  commonSearchLogService: CommonSearchLogService;
}
