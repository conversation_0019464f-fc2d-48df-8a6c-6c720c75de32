import { CommonForumService } from '../../../service/common/forum';
import { Provide, Post, Inject, Body, ALL, Get } from '@midwayjs/decorator';
import { CoolController, BaseController } from '@cool-midway/core';
import { CcForumThread } from '../../../entity/forum/CcForumThread';

/**
 * 论坛
 */
@Provide()
@CoolController({
  api: ['info'],
  entity: CcForumThread,
  service: CommonForumService,
})
export class CommonForumController extends BaseController {
  @Inject()
  commonForumService: CommonForumService;

  @Post('/getThreadUrlByPid')
  async getThreadUrlByPid(@Body(ALL) param: any) {
    return this.ok(await this.commonForumService.getThreadUrlByPid(param));
  }

  @Post('/updateShowMobile')
  async updateShowMobile(@Body(ALL) param: any) {
    return this.ok(await this.commonForumService.updateShowMobile(param));
  }

  @Post('/getShowMobile')
  async getShowMobile(@Body(ALL) param: any) {
    return this.ok(await this.commonForumService.getShowMobile(param));
  }

  @Post('/appFeatureSwitch')
  async appFeatureSwitch(@Body(ALL) param: any) {
    return this.ok(await this.commonForumService.appFeatureSwitch(param));
  }

  @Post('/stick')
  async stick(@Body(ALL) param: any) {
    return this.ok(await this.commonForumService.stick(param));
  }

  @Post('/highlightDigest')
  async highlightDigest(@Body(ALL) param: any) {
    return this.ok(await this.commonForumService.highlightDigest(param));
  }

  @Post('/publishToWWW')
  async publishToWWW(@Body(ALL) param: any) {
    return this.ok(await this.commonForumService.publishToWWW2(param));
  }

  @Post('/publishToForum')
  async publishToForum(@Body(ALL) param: any) {
    return this.ok(await this.commonForumService.publishToForum(param));
  }

  @Get('/nav')
  async nav() {
    return this.ok(await this.commonForumService.nav());
  }

  @Post('/subnav')
  async subnav(@Body(ALL) param: any) {
    return this.ok(await this.commonForumService.subnav(param));
  }

  @Get('/initSearchRecommandIndex', { summary: '初始化搜索推荐索引' })
  async initSearchRecommandIndex() {
    return this.ok(await this.commonForumService.initSearchRecommandIndex());
  }

  @Get('/initForumIndex', { summary: '初始化论坛索引' })
  async initForumIndex() {
    return this.ok(await this.commonForumService.initForumIndex());
  }

  @Get('/initForumIndexData', { summary: '初始化论坛数据' })
  async initForumData() {
    // return this.ok(await this.commonForumService.initForumIndexData());
  }

  @Post('/searchRecommendPage', { summary: '搜索推荐列表' })
  async searchRecommendPage(@Body(ALL) param: any) {
    return this.ok(await this.commonForumService.searchRecommendPage(param));
  }

  @Post('/searchRecommendAdd', { summary: '搜索推荐添加' })
  async searchRecommendAdd(@Body(ALL) param: any) {
    return this.ok(await this.commonForumService.searchRecommendAdd(param));
  }

  @Post('/searchRecommendUpdate', { summary: '搜索推荐更新' })
  async searchRecommendUpdate(@Body(ALL) param: any) {
    return this.ok(await this.commonForumService.searchRecommendUpdate(param));
  }

  @Post('/searchRecommendDelete', { summary: '搜索推荐删除' })
  async searchRecommendDelete(@Body(ALL) param: any) {
    return this.ok(await this.commonForumService.searchRecommendDelete(param));
  }

  @Post('/searchRecommendGroupList', { summary: '搜索推荐分组列表' })
  async searchRecommendGroupList() {
    return this.ok(await this.commonForumService.searchRecommendGroupList());
  }

  @Post('/searchRecommendGroupPage', { summary: '搜索推荐分组列表' })
  async searchRecommendGroupPage(@Body(ALL) param: any) {
    return this.ok(
      await this.commonForumService.searchRecommendGroupPage(param)
    );
  }

  @Post('/searchRecommendGroupAdd', { summary: '搜索推荐分组添加' })
  async searchRecommendGroupAdd(@Body(ALL) param: any) {
    return this.ok(
      await this.commonForumService.searchRecommendGroupAdd(param)
    );
  }

  @Post('/searchRecommendGroupUpdate', { summary: '搜索推荐分组更新' })
  async searchRecommendGroupUpdate(@Body(ALL) param: any) {
    return this.ok(
      await this.commonForumService.searchRecommendGroupUpdate(param)
    );
  }

  @Post('/searchRecommendGroupDelete', { summary: '搜索推荐分组删除' })
  async searchRecommendGroupDelete(@Body(ALL) param: any) {
    return this.ok(
      await this.commonForumService.searchRecommendGroupDelete(param)
    );
  }

  @Post('/threadWeightPage', { summary: '论坛帖子权重列表' })
  async threadWeightPage(@Body(ALL) param: any) {
    return this.ok(await this.commonForumService.threadWeightPage(param));
  }

  @Post('/threadWeight', { summary: '论坛帖子权重' })
  async threadWeight(@Body(ALL) param: any) {
    return this.ok(await this.commonForumService.threadWeight(param));
  }

  @Post('/threadWeightAdd', { summary: '论坛帖子权重添加' })
  async threadWeightAdd(@Body(ALL) param: any) {
    return this.ok(await this.commonForumService.threadWeightAdd(param));
  }

  @Post('/threadWeightDelete', { summary: '论坛帖子权重删除' })
  async threadWeightDelete(@Body(ALL) param: any) {
    return this.ok(await this.commonForumService.threadWeightDelete(param));
  }

  @Get('/fixForumPostData', { summary: '修复论坛快速回复帖子数据' })
  async fixForumPostData() {
    return this.ok(await this.commonForumService.fixForumPostData());
  }

  @Post('/sensitive')
  async sensitive(@Body(ALL) param: any) {
    return this.ok(await this.commonForumService.sensitive(param));
  }

  @Post('/sensitiveMonitorGet')
  async sensitiveMonitorGet(@Body(ALL) param: any) {
    return this.ok(await this.commonForumService.sensitiveMonitorGet(param));
  }

  @Post('/sensitiveMonitorUpdate')
  async sensitiveMonitorUpdate(@Body(ALL) param: any) {
    return this.ok(await this.commonForumService.sensitiveMonitorUpdate(param));
  }

  @Get('/exportSensitiveContentToExcel', { summary: '导出敏感内容到Excel文件' })
  async exportSensitiveContentToExcel() {
    return this.ok(await this.commonForumService.exportSensitiveContentToExcel());
  }
}
