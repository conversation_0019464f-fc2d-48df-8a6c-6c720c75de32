import { CommonSougouService } from '../../../service/common/sougou';
import { Provide, Post, Inject, Body, ALL, Get } from '@midwayjs/decorator';
import { CoolController, BaseController } from '@cool-midway/core';
import { CommonSougouEntity } from '../../../entity/common/sougou';

/**
 * 搜狗
 */
@Provide()
@CoolController({
  api: ['update'],
  entity: CommonSougouEntity,
  service: CommonSougouService,
})
export class CommonSougouController extends BaseController {
  @Inject()
  commonSougouService: CommonSougouService;

  @Post('/create', { summary: '创建' })
  async create(@Body(ALL) param: any) {
    await this.commonSougouService.create(param);
    return this.ok();
  }

  @Post('/search', { summary: '搜索' })
  async search(@Body(ALL) param: any) {
    return this.ok(await this.commonSougouService.search(param));
  }

  @Post('/more', { summary: '查询相邻数据' })
  async more(@Body(ALL) param: any) {
    const id = Number(param.id);
    const ids = [id];

    for (let i = 1; i <= 3; i++) {
      ids.push(Math.max(id - i, 0));
      ids.push(id + i);
    }

    param.ids = ids.sort((a, b) => a - b);

    return this.ok(await this.commonSougouService.more(param));
  }

  @Get('/initIndex', { summary: '初始化索引' })
  async initIndex() {
    return this.ok(await this.commonSougouService.initIndex());
  }

  @Post('/initData', { summary: '初始化数据' })
  async initData(@Body(ALL) param: any) {
    return this.ok(await this.commonSougouService.initData(param));
  }
}
