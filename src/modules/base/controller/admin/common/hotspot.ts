import { Provide, Inject, Post, Body, ALL } from '@midwayjs/decorator';
import { CoolController, BaseController } from '@cool-midway/core';
import { CommonHotspotEntity } from '../../../entity/common/hotspot';
import { CommonHotspotService } from '../../../service/common/hotspot';

/**
 * Hotspot
 */
@Provide()
@CoolController({
  api: ['update'],
  entity: CommonHotspotEntity,
  service: CommonHotspotService,
})
export class CommonOpenController extends BaseController {
  @Inject()
  commonHotspotService: CommonHotspotService;

  @Post('/hotspotCreate', { summary: '创建Hotspot' })
  async tagUserCreate(@Body(ALL) param: any) {
    return this.ok(await this.commonHotspotService.hotspotCreate(param));
  }

  @Post('/hotspotAdd', { summary: '添加Hotspot' })
  async hotspotAdd(@Body(ALL) param: any) {
    return this.ok(await this.commonHotspotService.hotspotAdd(param));
  }

  @Post('/hotspotUpdate', { summary: '编辑Hotspot' })
  async hotspotUpdate(@Body(ALL) param: any) {
    return this.ok(await this.commonHotspotService.hotspotUpdate(param));
  }

  @Post('/hotspotDelete', { summary: '删除Hotspot' })
  async hotspotDelete(@Body(ALL) param: any) {
    return this.ok(await this.commonHotspotService.hotspotDelete(param));
  }

  @Post('/hotSpotPage', { summary: 'HotSpot列表' })
  async hotSpotPage(@Body(ALL) param: any) {
    return this.ok(await this.commonHotspotService.hotSpotPage(param));
  }
}
