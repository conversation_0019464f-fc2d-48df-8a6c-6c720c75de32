import {
  Provide,
  Inject,
  Get,
  Post,
  Body,
  Param,
  Query,
  ALL,
  WSController,
  OnWSConnection,
  OnWSMessage,
} from '@midwayjs/decorator';
import { CoolController, BaseController } from '@cool-midway/core';
import { Context } from '@midwayjs/socketio';
import { ChatgptSessionEntity } from '../../../entity/chatgpt/chatgptSession';
import { ChatgptService } from '../../../service/common/chatgpt';
import { AiVenderModelEntity } from '../../../entity/chatgpt/aiVenderModel';
import { InjectEntityModel } from '@midwayjs/typeorm';
import { Repository } from 'typeorm';

/**
 * ChatGPT
 */
@Provide()
@WSController('/ai/chatgpt')
@CoolController({
  api: ['info'],
  entity: ChatgptSessionEntity,
  service: ChatgptService,
})
export class CommonChatgptController extends BaseController {
  @Inject()
  ctx: Context;

  @InjectEntityModel(AiVenderModelEntity)
  aiVenderModelEntity: Repository<AiVenderModelEntity>;

  @Inject()
  chatgptService: ChatgptService;

  @Post('/gptsAuthPage', { summary: 'GPTs验证用户列表' })
  async gptsAuthPage(@Body(ALL) param: any) {
    return this.ok(await this.chatgptService.gptsAuthPage(param));
  }

  @Post('/gptsAuthAdd', { summary: 'GPTs验证用户添加' })
  async gptsAuthAdd(@Body(ALL) param: any) {
    return this.ok(await this.chatgptService.gptsAuthAdd(param));
  }

  @Post('/gptsAuthUpdate', { summary: 'GPTs验证用户更新' })
  async gptsAuthUpdate(@Body(ALL) param: any) {
    return this.ok(await this.chatgptService.gptsAuthUpdate(param));
  }

  @Post('/gptsAuthDelete', { summary: 'GPTs验证用户删除' })
  async gptsAuthDelete(@Body(ALL) param: any) {
    return this.ok(await this.chatgptService.gptsAuthDelete(param));
  }

  @Post('/voiceToText', { summary: '语音转文本' })
  async voiceToText(@Body(ALL) param: any) {
    const aiVM = await this.aiVenderModelEntity.findOneBy({ id: param.model });

    return this.ok(await this.chatgptService.voiceToText(param, aiVM));
  }

  @Post('/textToVoice', { summary: '文转语音' })
  async textToVoice(@Body(ALL) param: any) {
    const aiVM = await this.aiVenderModelEntity.findOneBy({ id: param.model });

    return this.ok(await this.chatgptService.textToVoice(param, aiVM));
  }

  @Post('/generateImageFromText', { summary: '文生图' })
  async generateImageFromText(@Body(ALL) param: any) {
    const aiVM = await this.aiVenderModelEntity.findOneBy({ id: param.model });

    return this.ok(
      await this.chatgptService.generateImageFromText(param, aiVM)
    );
  }

  @Get('/session/:sessionid', { summary: '获取Session对应Tag' })
  async sessionTags(@Param('sessionid') sessionid: number) {
    return this.ok(await this.chatgptService.sessionTags(sessionid));
  }

  @Post('/bindTag', { summary: '绑定Tag' })
  async bindTag(@Body(ALL) param: any) {
    return this.ok(await this.chatgptService.bindTag(param));
  }

  @Post('/unbindTag', { summary: '解除绑定Tag' })
  async unbindTag(@Body(ALL) param: any) {
    return this.ok(await this.chatgptService.unbindTag(param));
  }

  @Get('/tags', { summary: 'Tag列表' })
  async tags(@Query('sessionid') sessionid: number) {
    return this.ok(await this.chatgptService.tags(sessionid));
  }

  @Post('/tag', { summary: '添加Tag' })
  async addTag(@Body(ALL) param: any) {
    return this.ok(await this.chatgptService.addTag(param));
  }

  @Post('/delTag', { summary: '删除Tag' })
  async delTag(@Body(ALL) param: any) {
    return this.ok(await this.chatgptService.delTag(param));
  }

  @Post('/details', { summary: '问题列表' })
  async details(@Body(ALL) param: any) {
    return this.ok(await this.chatgptService.details(param));
  }

  @Post('/updateSubject', { summary: '更新标题' })
  async updateSubject(@Body(ALL) param: any) {
    return this.ok(await this.chatgptService.updateSubject(param));
  }

  @Post('/deleteSession', { summary: '软删除会话' })
  async deleteSession(@Body(ALL) param: any) {
    return this.ok(await this.chatgptService.deleteSession(param));
  }

  @Post('/destroySession', { summary: '物理删除会话' })
  async destroySession(@Body(ALL) param: any) {
    return this.ok(await this.chatgptService.destroySession(param));
  }

  @Post('/sessions', { summary: '会话列表' })
  async sessions(@Body(ALL) param: any) {
    return this.ok(await this.chatgptService.sessions(param));
  }

  @Post('/aiVender', { summary: 'AI供应商' })
  async aiVender(@Body(ALL) param: any) {
    return this.ok(await this.chatgptService.aiVender(param));
  }

  @Post('/aiVenderModel', { summary: 'AI供应商模型' })
  async aiVenderModel(@Body(ALL) param: any) {
    return this.ok(await this.chatgptService.aiVenderModel(param));
  }

  @Get('/users', { summary: '用户列表' })
  async users() {
    return this.ok(await this.chatgptService.users());
  }

  @Get('/chatgpt4', { summary: 'ChatGPT4.0' })
  async chatgpt4() {
    return this.ok();
  }

  @OnWSConnection()
  async onConnectionMethod() {
    this.ctx.emit('sys', '连接成功');
  }

  @OnWSMessage('conversation')
  async gotMessage(param) {
    await this.chatgptService.conversation(param);
  }
}
