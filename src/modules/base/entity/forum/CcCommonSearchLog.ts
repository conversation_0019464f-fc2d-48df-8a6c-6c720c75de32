import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';

@Entity('cc_common_search_log', { synchronize: false })
export class CcCommonSearchLog {
  @PrimaryGeneratedColumn({ type: 'bigint', name: 'id', unsigned: true })
  id: string;

  @Column('varchar', { name: 'content', length: 1000, default: () => "''" })
  content: string;

  @Column({ type: 'int', name: 'uid', unsigned: true })
  uid: number;

  @Column('varchar', { name: 'username', length: 255, default: () => "''" })
  username: string;

  @Column('char', { name: 'ip', length: 15 })
  ip: string;

  @Column('int', { name: 'dateline' })
  dateline: number;
}
