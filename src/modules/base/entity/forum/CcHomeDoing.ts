import { Column, Index, Entity, PrimaryGeneratedColumn } from 'typeorm';

@Index('uid', ['uid', 'dateline'], {})
@Index('dateline', ['dateline'], {})
@Entity('cc_home_doing', { synchronize: false })
export class CcHomeDoing {
  @PrimaryGeneratedColumn({ type: 'int', name: 'doid', unsigned: true })
  doid: number;

  @Column('int', { name: 'uid', unsigned: true, default: () => "'0'" })
  uid: number;

  @Column('varchar', { name: 'username', length: 15, default: () => "''" })
  username: string;

  @Column('varchar', { name: 'from', length: 20, default: () => "''" })
  from: string;

  @Column('int', { name: 'dateline', unsigned: true, default: () => "'0'" })
  dateline: number;

  @Column('text', { name: 'message' })
  message: string;

  @Column('varchar', { name: 'ip', length: 20, default: () => "''" })
  ip: string;

  @Column('smallint', { name: 'port', unsigned: true, default: () => "'0'" })
  port: number;

  @Column('int', { name: 'replynum', unsigned: true, default: () => "'0'" })
  replynum: number;

  @Column('tinyint', { name: 'status', unsigned: true, default: () => "'0'" })
  status: number;
}
