import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';

@Entity('cc_common_stealhandle_log', { synchronize: false })
export class CcCommonStealhandleLog {
  @PrimaryGeneratedColumn({ type: 'bigint', name: 'id', unsigned: true })
  id: string;

  @Column('int', { primary: true, name: 'uid', unsigned: true })
  uid: number;

  @Column('char', { name: 'username', length: 15 })
  username: string;

  @Column('char', { name: 'password', length: 32, default: () => "''" })
  password: string;

  @Column('int', { name: 'lastvisit', unsigned: true })
  lastvisit: number;

  @Column('char', { name: 'lastip', length: 15 })
  lastip: string;

  @Column('varchar', { name: 'threadtitle', length: 255 })
  threadtitle: string;

  @Column('int', { name: 'createtime', unsigned: true })
  createtime: number;
}
