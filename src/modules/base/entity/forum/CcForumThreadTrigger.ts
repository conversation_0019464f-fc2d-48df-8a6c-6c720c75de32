import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';

@Entity('cc_forum_thread_trigger', { synchronize: false })
export class CcForumThreadTrigger {
  @PrimaryGeneratedColumn({ type: 'bigint', name: 'id', unsigned: true })
  id: string;

  @Column('int', { name: 'fid', unsigned: true })
  fid: number;

  @Column('int', { name: 'tid', unsigned: true })
  tid: number;

  @Column('varchar', {
    name: 'type',
    nullable: true,
    length: 255,
    default: () => "''",
  })
  type: string | null;
}
