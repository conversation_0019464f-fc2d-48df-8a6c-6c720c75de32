import { Column, Index, Entity, PrimaryGeneratedColumn } from 'typeorm';

@Index('uid', ['uid', 'action', 'dateline'], {})
@Entity('cc_common_member_crime', { synchronize: false })
export class CcCommonMemberCrime {
  @PrimaryGeneratedColumn({ type: 'int', name: 'cid', unsigned: true })
  cid: number;

  @Column('int', { name: 'uid', unsigned: true, default: () => "'0'" })
  uid: number;

  @Column('int', {
    name: 'operatorid',
    unsigned: true,
    default: () => "'0'",
  })
  operatorid: number;

  @Column('varchar', { name: 'operator', length: 15 })
  operator: string;

  @Column('tinyint', { name: 'action' })
  action: number;

  @Column('text', { name: 'reason' })
  reason: string;

  @Column('int', { name: 'dateline', unsigned: true, default: () => "'0'" })
  dateline: number;
}
