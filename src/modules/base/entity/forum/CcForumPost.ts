import { Column, Entity } from 'typeorm';

@Entity('cc_forum_post', { synchronize: false })
export class CcForumPost {
  @Column('int', { name: 'pid', unique: true, unsigned: true })
  pid: number;

  @Column('int', { name: 'fid', unsigned: true, default: () => "'0'" })
  fid: number;

  @Column('int', {
    primary: true,
    name: 'tid',
    unsigned: true,
    default: () => "'0'",
  })
  tid: number;

  @Column('tinyint', { name: 'first', width: 1, default: () => "'0'" })
  first: boolean;

  @Column('varchar', { name: 'author', length: 15, default: () => "''" })
  author: string;

  @Column('int', {
    name: 'authorid',
    unsigned: true,
    default: () => "'0'",
  })
  authorid: number;

  @Column('varchar', { name: 'subject', length: 80, default: () => "''" })
  subject: string;

  @Column('int', { name: 'dateline', unsigned: true, default: () => "'0'" })
  dateline: number;

  @Column('text', { name: 'message' })
  message: string;

  @Column('varchar', { name: 'useip', length: 15, default: () => "''" })
  useip: string;

  @Column('smallint', { name: 'port', unsigned: true, default: () => "'0'" })
  port: number;

  @Column('tinyint', { name: 'invisible', width: 1, default: () => "'0'" })
  invisible: boolean;

  @Column('tinyint', { name: 'anonymous', width: 1, default: () => "'0'" })
  anonymous: boolean;

  @Column('tinyint', { name: 'usesig', width: 1, default: () => "'0'" })
  usesig: boolean;

  @Column('tinyint', { name: 'htmlon', width: 1, default: () => "'0'" })
  htmlon: boolean;

  @Column('tinyint', { name: 'bbcodeoff', width: 1, default: () => "'0'" })
  bbcodeoff: boolean;

  @Column('tinyint', { name: 'smileyoff', width: 1, default: () => "'0'" })
  smileyoff: boolean;

  @Column('tinyint', { name: 'parseurloff', width: 1, default: () => "'0'" })
  parseurloff: boolean;

  @Column('tinyint', { name: 'attachment', width: 1, default: () => "'0'" })
  attachment: boolean;

  @Column('smallint', { name: 'rate', default: () => "'0'" })
  rate: number;

  @Column('tinyint', {
    name: 'ratetimes',
    unsigned: true,
    default: () => "'0'",
  })
  ratetimes: number;

  @Column('int', { name: 'status', default: () => "'0'" })
  status: number;

  @Column('varchar', { name: 'tags', length: 255, default: () => "'0'" })
  tags: string;

  @Column('tinyint', { name: 'comment', width: 1, default: () => "'0'" })
  comment: boolean;

  @Column('int', { name: 'replycredit', default: () => "'0'" })
  replycredit: number;

  @Column('tinyint', { name: 'replytype', default: () => "'0'" })
  replytype: number;

  position: number;
}
