import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';

@Entity('cc_common_userlogin_log', { synchronize: false })
export class CcCommonUserloginLog {
  @PrimaryGeneratedColumn({ type: 'bigint', name: 'id', unsigned: true })
  id: string;

  @Column('int', { primary: true, name: 'uid', unsigned: true })
  uid: number;

  @Column('char', { name: 'email', length: 40 })
  email: string;

  @Column('char', { name: 'username', length: 15 })
  username: string;

  @Column('int', { name: 'dateline', unsigned: true })
  dateline: number;

  @Column('char', { name: 'ip', length: 15 })
  ip: string;

  @Column('tinyint', { name: 'loginsuccess', width: 1 })
  loginsuccess: boolean;

  @Column('varchar', { name: 'browserinfo', length: 255 })
  browserinfo: string;

  @Column('tinyint', { name: 'issteal', width: 1, default: () => "'0'" })
  issteal: boolean;

  @Column('varchar', { name: 'referer', nullable: true, length: 255 })
  referer: string | null;

  @Column('varchar', { name: 'phonestamp', nullable: true, length: 11 })
  phonestamp: string | null;

  @Column('tinyint', { name: 'loginmethod', nullable: true })
  loginmethod: number | null;
}
