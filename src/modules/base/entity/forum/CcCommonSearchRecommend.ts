import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';

@Entity('cc_common_search_recommend', { synchronize: false })
export class CcCommonSearchRecommend {
  @PrimaryGeneratedColumn({ type: 'bigint', name: 'id', unsigned: true })
  id: string;

  @Column('bigint', { name: 'gid', unsigned: true, default: () => 0 })
  gid: string;

  @Column('varchar', { name: 'keyword', length: 255, default: () => "''" })
  keyword: string;

  @Column('text', { name: 'content' })
  content: string;

  @Column('tinyint', { name: 'status' })
  status: boolean;

  @Column('int', { name: 'begin' })
  begin: number;

  @Column('int', { name: 'end' })
  end: number;

  @Column('smallint', { name: 'order' })
  order: number;

  @Column('int', { name: 'dateline' })
  dateline: number;
}
