import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';

@Entity('cc_common_search_recommend_group', { synchronize: false })
export class CcCommonSearchRecommendGroup {
  @PrimaryGeneratedColumn({ type: 'bigint', name: 'id', unsigned: true })
  id: string;

  @Column('varchar', { name: 'name', length: 255, default: () => "''" })
  name: string;

  @Column('int', { name: 'dateline' })
  dateline: number;
}
