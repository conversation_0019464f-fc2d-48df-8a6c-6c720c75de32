import { Column, Index, Entity } from 'typeorm';

@Index('isnew', ['isnew'], {})
@Index('lastdateline', ['uid', 'lastdateline'], {})
@Index('lastupdate', ['uid', 'lastupdate'], {})
@Entity('cc_ucenter_pm_members', { synchronize: false })
export class CcUcenterPmMembers {
  @Column('int', {
    primary: true,
    name: 'plid',
    unsigned: true,
    default: () => "'0'",
  })
  plid: number;

  @Column('int', {
    primary: true,
    name: 'uid',
    unsigned: true,
    default: () => "'0'",
  })
  uid: number;

  @Column('tinyint', { name: 'isnew', unsigned: true, default: () => "'0'" })
  isnew: number;

  @Column('int', { name: 'pmnum', unsigned: true, default: () => "'0'" })
  pmnum: number;

  @Column('int', { name: 'lastupdate', unsigned: true, default: () => "'0'" })
  lastupdate: number;

  @Column('int', { name: 'lastdateline', unsigned: true, default: () => "'0'" })
  lastdateline: number;
}
