import { Column, Index, Entity } from 'typeorm';

@Index('tid', ['tid', 'uid'], {})
@Entity('cc_forum_threadpartake', { synchronize: false })
export class CcForumThreadpartake {
  @Column('int', {
    primary: true,
    name: 'tid',
    unsigned: true,
    default: () => "'0'",
  })
  tid: number;

  @Column('int', { name: 'uid', unsigned: true, default: () => "'0'" })
  uid: number;

  @Column('int', { name: 'dateline', unsigned: true, default: () => "'0'" })
  dateline: number;
}
