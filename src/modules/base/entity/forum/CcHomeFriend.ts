import { Column, Index, Entity } from 'typeorm';

@Index('fuid', ['fuid'], {})
@Index('uid', ['uid', 'num', 'dateline'], {})
@Entity('cc_home_friend', { synchronize: false })
export class CcHomeFriend {
  @Column('int', {
    primary: true,
    name: 'uid',
    unsigned: true,
    default: () => "'0'",
  })
  uid: number;

  @Column('int', {
    primary: true,
    name: 'fuid',
    unsigned: true,
    default: () => "'0'",
  })
  fuid: number;

  @Column('varchar', { name: 'fusername', length: 15, default: () => "''" })
  fusername: string;

  @Column('smallint', { name: 'gid', unsigned: true, default: () => "'0'" })
  gid: number;

  @Column('int', { name: 'num', unsigned: true, default: () => "'0'" })
  num: number;

  @Column('int', { name: 'dateline', unsigned: true, default: () => "'0'" })
  dateline: number;

  @Column('varchar', { name: 'note', length: 255, default: () => "''" })
  note: string;
}
