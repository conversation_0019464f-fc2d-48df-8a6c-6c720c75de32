import { Column, Index, Entity } from 'typeorm';

@Index('tid', ['tid'], {})
@Index('pid', ['pid'], {})
@Index('uid', ['uid'], {})
@Entity('cc_forum_attachment_6', { synchronize: false })
export class CcForumAttachment_6 {
  @Column('int', { primary: true, name: 'aid', unsigned: true })
  aid: number;

  @Column('int', { name: 'tid', unsigned: true, default: () => "'0'" })
  tid: number;

  @Column('int', { name: 'pid', unsigned: true, default: () => "'0'" })
  pid: number;

  @Column('int', { name: 'uid', unsigned: true, default: () => "'0'" })
  uid: number;

  @Column('int', { name: 'dateline', unsigned: true, default: () => "'0'" })
  dateline: number;

  @Column('varchar', { name: 'filename', length: 255, default: () => "''" })
  filename: string;

  @Column('int', { name: 'filesize', unsigned: true, default: () => "'0'" })
  filesize: number;

  @Column('varchar', { name: 'attachment', length: 255, default: () => "''" })
  attachment: string;

  @Column('tinyint', { name: 'remote', unsigned: true, default: () => "'0'" })
  remote: number;

  @Column('varchar', { name: 'description', length: 255 })
  description: string;

  @Column('tinyint', { name: 'readperm', unsigned: true, default: () => "'0'" })
  readperm: number;

  @Column('smallint', { name: 'price', unsigned: true, default: () => "'0'" })
  price: number;

  @Column('tinyint', { name: 'isimage', width: 1, default: () => "'0'" })
  isimage: boolean;

  @Column('smallint', { name: 'width', unsigned: true, default: () => "'0'" })
  width: number;

  @Column('tinyint', { name: 'thumb', unsigned: true, default: () => "'0'" })
  thumb: number;

  @Column('int', { name: 'picid', default: () => "'0'" })
  picid: number;

  @Column('smallint', { name: 'height', unsigned: true, default: () => "'0'" })
  height: number;

  @Column('smallint', {
    name: 'thumbwidth',
    unsigned: true,
    default: () => "'0'",
  })
  thumbwidth: number;

  @Column('smallint', {
    name: 'thumbheight',
    unsigned: true,
    default: () => "'0'",
  })
  thumbheight: number;
}
