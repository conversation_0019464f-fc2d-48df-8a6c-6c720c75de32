import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';

@Entity('cc_common_advertisement', { synchronize: false })
export class CcCommonAdvertisement {
  @PrimaryGeneratedColumn({ type: 'int', name: 'advid', unsigned: true })
  advid: number;

  @Column('tinyint', { name: 'available', width: 1, default: () => "'0'" })
  available: boolean;

  @Column('varchar', { name: 'type', length: 50, default: () => "'0'" })
  type: string;

  @Column('tinyint', { name: 'displayorder', default: () => "'0'" })
  displayorder: number;

  @Column('varchar', { name: 'title', length: 255, default: () => "''" })
  title: string;

  @Column('text', { name: 'targets' })
  targets: string;

  @Column('text', { name: 'parameters' })
  parameters: string;

  @Column('text', { name: 'code' })
  code: string;

  @Column('int', { name: 'starttime', unsigned: true, default: () => "'0'" })
  starttime: number;

  @Column('int', { name: 'endtime', unsigned: true, default: () => "'0'" })
  endtime: number;
}
