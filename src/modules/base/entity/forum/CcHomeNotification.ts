import { Column, Index, Entity, PrimaryGeneratedColumn } from 'typeorm';

@Index('from_id', ['from_id', 'from_idtype'], {})
@Index('uid', ['uid', 'new'], {})
@Index('category', ['uid', 'category', 'dateline'], {})
@Index('by_type', ['uid', 'type', 'dateline'], {})
@Entity('cc_home_notification', { synchronize: false })
export class CcHomeNotification {
  @PrimaryGeneratedColumn({ type: 'int', name: 'id', unsigned: true })
  id: number;

  @Column('int', { name: 'uid', unsigned: true, default: () => "'0'" })
  uid: number;

  @Column('varchar', { name: 'type', length: 20, default: () => "''" })
  type: string;

  @Column('tinyint', { name: 'new', width: 1, default: () => "'0'" })
  new: boolean;

  @Column('int', {
    name: 'authorid',
    unsigned: true,
    default: () => "'0'",
  })
  authorid: number;

  @Column('varchar', { name: 'author', length: 15, default: () => "''" })
  author: string;

  @Column('text', { name: 'note' })
  note: string;

  @Column('int', { name: 'dateline', unsigned: true, default: () => "'0'" })
  dateline: number;

  @Column('int', {
    name: 'from_id',
    unsigned: true,
    default: () => "'0'",
  })
  from_id: number;

  @Column('varchar', { name: 'from_idtype', length: 20, default: () => "''" })
  from_idtype: string;

  @Column('int', {
    name: 'from_num',
    unsigned: true,
    default: () => "'0'",
  })
  from_num: number;

  @Column('tinyint', { name: 'category', width: 1, default: () => "'0'" })
  category: boolean;
}
