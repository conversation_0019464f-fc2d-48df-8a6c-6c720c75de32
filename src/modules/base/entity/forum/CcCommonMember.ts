import { Column, Index, Entity, PrimaryGeneratedColumn } from 'typeorm';

@Index('username', ['username'], { unique: true })
@Entity('cc_common_member', { synchronize: false })
export class CcCommonMember {
  @PrimaryGeneratedColumn({ type: 'int', name: 'uid', unsigned: true })
  uid: number;

  @Column('char', { name: 'email', length: 40, default: () => "''" })
  email: string;

  @Column('char', {
    name: 'username',
    unique: true,
    length: 15,
    default: () => "''",
  })
  username: string;

  @Column('char', { name: 'password', length: 32, default: () => "''" })
  password: string;

  @Column('int', { name: 'status', default: () => "'0'" })
  status: number;

  @Column('int', { name: 'emailstatus', default: () => "'0'" })
  emailstatus: number;

  @Column('int', { name: 'avatarstatus', default: () => "'0'" })
  avatarstatus: number;

  @Column('int', { name: 'videophotostatus', default: () => "'0'" })
  videophotostatus: number;

  @Column('int', { name: 'adminid', default: () => "'0'" })
  adminid: number;

  @Column('int', { name: 'groupid', unsigned: true, default: () => "'0'" })
  groupid: number;

  @Column('int', { name: 'groupexpiry', unsigned: true, default: () => "'0'" })
  groupexpiry: number;

  @Column('char', { name: 'extgroupids', length: 20, default: () => "''" })
  extgroupids: string;

  @Column('int', { name: 'regdate', unsigned: true, default: () => "'0'" })
  regdate: number;

  @Column('int', { name: 'credits', default: () => "'0'" })
  credits: number;

  @Column('int', { name: 'notifysound', default: () => "'0'" })
  notifysound: number;

  @Column('char', { name: 'timeoffset', length: 4, default: () => "''" })
  timeoffset: string;

  @Column('int', { name: 'newpm', unsigned: true, default: () => "'0'" })
  newpm: number;

  @Column('int', { name: 'newprompt', unsigned: true, default: () => "'0'" })
  newprompt: number;

  @Column('int', { name: 'accessmasks', default: () => "'0'" })
  accessmasks: number;

  @Column('int', { name: 'allowadmincp', default: () => "'0'" })
  allowadmincp: number;

  @Column('int', { name: 'onlyacceptfriendpm', default: () => "'0'" })
  onlyacceptfriendpm: number;

  @Column('int', { name: 'conisbind', unsigned: true, default: () => "'0'" })
  conisbind: number;

  @Column('int', { name: 'freeze', default: () => "'0'" })
  freeze: number;

  @Column('varchar', { name: 'avatar', length: 255 })
  avatar: string;
}
