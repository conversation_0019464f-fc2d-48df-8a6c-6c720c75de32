import { Column, Index, Entity, PrimaryGeneratedColumn } from 'typeorm';

@Index(
  'cc_common_member_block_user_uid_blocked_uid',
  ['uid', 'blocked_uid'],
  {}
)
@Entity('cc_common_member_block_user', { synchronize: false })
export class CcCommonMemberBlockUser {
  @PrimaryGeneratedColumn({ type: 'int', name: 'id', unsigned: true })
  id: number;

  @Column('int', { name: 'uid', unsigned: true, default: () => "'0'" })
  uid: number;

  @Column('int', { name: 'blocked_uid', unsigned: true, default: () => "'0'" })
  blocked_uid: number;

  @Column('int', { name: 'dateline', unsigned: true, default: () => "'0'" })
  dateline: number;
}
