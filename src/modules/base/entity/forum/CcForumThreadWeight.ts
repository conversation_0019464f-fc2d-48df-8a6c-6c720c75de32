import { Column, Index, Entity, PrimaryGeneratedColumn } from 'typeorm';

@Entity('cc_forum_thread_weight', { synchronize: false })
export class CcForumThreadWeight {
  @PrimaryGeneratedColumn({ type: 'int', name: 'id', unsigned: true })
  id: number;

  @Index({ unique: true })
  @Column({ type: 'int', name: 'tid', unsigned: true })
  tid: number;

  @Column('smallint', { name: 'weight', unsigned: true, default: () => "'0'" })
  weight: number;

  @Column('int', { name: 'dateline', unsigned: true, default: () => "'0'" })
  dateline: number;
}
