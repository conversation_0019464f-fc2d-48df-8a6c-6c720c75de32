import { Column, Entity } from 'typeorm';

@Entity('cc_common_member_field_home', { synchronize: false })
export class CcCommonMemberFieldHome {
  @Column('int', { primary: true, name: 'uid', unsigned: true })
  uid: number;

  @Column('varchar', { name: 'videophoto', length: 255, default: () => "''" })
  videophoto: string;

  @Column('varchar', { name: 'spacename', length: 255, default: () => "''" })
  spacename: string;

  @Column('varchar', {
    name: 'spacedescription',
    length: 255,
    default: () => "''",
  })
  spacedescription: string;

  @Column('char', { name: 'domain', length: 15, default: () => "''" })
  domain: string;

  @Column('int', { name: 'addsize', unsigned: true, default: () => "'0'" })
  addsize: number;

  @Column('int', { name: 'addfriend', unsigned: true, default: () => "'0'" })
  addfriend: number;

  @Column('int', { name: 'menunum', unsigned: true, default: () => "'0'" })
  menunum: number;

  @Column('varchar', { name: 'theme', length: 20, default: () => "''" })
  theme: string;

  @Column('text', { name: 'spacecss' })
  spacecss: string;

  @Column('text', { name: 'blockposition' })
  blockposition: string;

  @Column('text', { name: 'recentnote' })
  recentnote: string;

  @Column('text', { name: 'spacenote' })
  spacenote: string;

  @Column('text', { name: 'privacy' })
  privacy: string;

  @Column('text', { name: 'feedfriend' })
  feedfriend: string;

  @Column('text', { name: 'acceptemail' })
  acceptemail: string;

  @Column('text', { name: 'magicgift' })
  magicgift: string;

  @Column('text', { name: 'stickblogs' })
  stickblogs: string;
}
