import { Column, Entity } from 'typeorm';

@Entity('cc_common_member_count', { synchronize: false })
export class CcCommonMemberCount {
  @Column('int', { primary: true, name: 'uid', unsigned: true })
  uid: number;

  @Column('int', { name: 'extcredits1', default: () => "'0'" })
  extcredits1: number;

  @Column('int', { name: 'extcredits2', default: () => "'0'" })
  extcredits2: number;

  @Column('int', { name: 'extcredits3', default: () => "'0'" })
  extcredits3: number;

  @Column('int', { name: 'extcredits4', default: () => "'0'" })
  extcredits4: number;

  @Column('int', { name: 'extcredits5', default: () => "'0'" })
  extcredits5: number;

  @Column('int', { name: 'extcredits6', default: () => "'0'" })
  extcredits6: number;

  @Column('int', { name: 'extcredits7', default: () => "'0'" })
  extcredits7: number;

  @Column('int', { name: 'extcredits8', default: () => "'0'" })
  extcredits8: number;

  @Column('int', { name: 'friends', unsigned: true, default: () => "'0'" })
  friends: number;

  @Column('int', { name: 'posts', unsigned: true, default: () => "'0'" })
  posts: number;

  @Column('int', { name: 'threads', unsigned: true, default: () => "'0'" })
  threads: number;

  @Column('int', { name: 'digestposts', unsigned: true, default: () => "'0'" })
  digestposts: number;

  @Column('int', { name: 'doings', unsigned: true, default: () => "'0'" })
  doings: number;

  @Column('int', { name: 'blogs', unsigned: true, default: () => "'0'" })
  blogs: number;

  @Column('int', { name: 'albums', unsigned: true, default: () => "'0'" })
  albums: number;

  @Column('int', { name: 'sharings', unsigned: true, default: () => "'0'" })
  sharings: number;

  @Column('int', { name: 'attachsize', unsigned: true, default: () => "'0'" })
  attachsize: number;

  @Column('int', { name: 'views', unsigned: true, default: () => "'0'" })
  views: number;

  @Column('int', { name: 'oltime', unsigned: true, default: () => "'0'" })
  oltime: number;

  @Column('int', { name: 'todayattachs', unsigned: true, default: () => "'0'" })
  todayattachs: number;

  @Column('int', {
    name: 'todayattachsize',
    unsigned: true,
    default: () => "'0'",
  })
  todayattachsize: number;

  @Column('int', { name: 'feeds', unsigned: true, default: () => "'0'" })
  feeds: number;

  @Column('int', { name: 'follower', unsigned: true, default: () => "'0'" })
  follower: number;

  @Column('int', { name: 'following', unsigned: true, default: () => "'0'" })
  following: number;

  @Column('int', { name: 'newfollower', unsigned: true, default: () => "'0'" })
  newfollower: number;

  @Column('int', { name: 'blacklist', unsigned: true, default: () => "'0'" })
  blacklist: number;
}
