import { Column, Index, Entity, PrimaryGeneratedColumn } from 'typeorm';

@Index('tid', ['tid'], {})
@Index('pid', ['pid'], {})
@Index('uid', ['uid'], {})
@Entity('cc_forum_attachment', { synchronize: false })
export class CcForumAttachment {
  @PrimaryGeneratedColumn({ type: 'int', name: 'aid', unsigned: true })
  aid: number;

  @Column('int', { name: 'tid', unsigned: true, default: () => "'0'" })
  tid: number;

  @Column('int', { name: 'pid', unsigned: true, default: () => "'0'" })
  pid: number;

  @Column('int', { name: 'uid', unsigned: true, default: () => "'0'" })
  uid: number;

  @Column('tinyint', { name: 'tableid', unsigned: true, default: () => "'0'" })
  tableid: number;

  @Column('int', { name: 'downloads', default: () => "'0'" })
  downloads: number;
}
