import { Column, Index, Entity, PrimaryGeneratedColumn } from 'typeorm';

@Index('username', ['username'], { unique: true })
@Entity('cc_ucenter_members', { synchronize: false })
export class CcUcenterMembers {
  @PrimaryGeneratedColumn({ type: 'int', name: 'uid', unsigned: true })
  uid: number;

  @Column('char', {
    name: 'username',
    unique: true,
    length: 15,
    default: () => "''",
  })
  username: string;

  @Column('char', { name: 'password', length: 32, default: () => "''" })
  password: string;

  @Column('char', { name: 'email', length: 32, default: () => "''" })
  email: string;

  @Column('char', { name: 'myid', length: 30, default: () => "''" })
  myid: string;

  @Column('char', { name: 'myidkey', length: 16, default: () => "''" })
  myidkey: string;

  @Column('char', { name: 'regip', length: 15, default: () => "''" })
  regip: string;

  @Column('int', { name: 'regdate', unsigned: true, default: () => "'0'" })
  regdate: number;

  @Column('int', { name: 'lastloginip', default: () => "'0'" })
  lastloginip: number;

  @Column('int', {
    name: 'lastlogintime',
    unsigned: true,
    default: () => "'0'",
  })
  lastlogintime: number;

  @Column('char', { name: 'salt', length: 6 })
  salt: string;

  @Column('char', { name: 'secques', length: 8, default: () => "''" })
  secques: string;
}
