import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';

@Entity('cc_forum_post_trigger', { synchronize: false })
export class CcForumPostTrigger {
  @PrimaryGeneratedColumn({ type: 'bigint', name: 'id', unsigned: true })
  id: string;

  @Column('int', { name: 'fid', unsigned: true })
  fid: number;

  @Column('int', { name: 'pid', unsigned: true, default: () => "'0'" })
  pid: number;

  @Column('varchar', {
    name: 'type',
    nullable: true,
    length: 255,
    default: () => "''",
  })
  type: string | null;
}
