import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';

@Entity('cc_forum_thread', { synchronize: false })
export class CcForumThread {
  @PrimaryGeneratedColumn({ type: 'int', name: 'tid', unsigned: true })
  tid: number;

  @Column('int', { name: 'fid', unsigned: true, default: () => "'0'" })
  fid: number;

  @Column('smallint', {
    name: 'posttableid',
    unsigned: true,
    default: () => "'0'",
  })
  posttableid: number;

  @Column('smallint', { name: 'typeid', unsigned: true, default: () => "'0'" })
  typeid: number;

  @Column('smallint', { name: 'sortid', unsigned: true, default: () => "'0'" })
  sortid: number;

  @Column('tinyint', { name: 'readperm', unsigned: true, default: () => "'0'" })
  readperm: number;

  @Column('smallint', { name: 'price', default: () => "'0'" })
  price: number;

  @Column('char', { name: 'author', length: 15, default: () => "''" })
  author: string;

  @Column('int', {
    name: 'authorid',
    unsigned: true,
    default: () => "'0'",
  })
  authorid: number;

  @Column('char', { name: 'subject', length: 80, default: () => "''" })
  subject: string;

  @Column('int', { name: 'dateline', unsigned: true, default: () => "'0'" })
  dateline: number;

  @Column('int', { name: 'lastpost', unsigned: true, default: () => "'0'" })
  lastpost: number;

  @Column('char', { name: 'lastposter', length: 15, default: () => "''" })
  lastposter: string;

  @Column('int', { name: 'views', unsigned: true, default: () => "'0'" })
  views: number;

  @Column('int', {
    name: 'replies',
    unsigned: true,
    default: () => "'0'",
  })
  replies: number;

  @Column('tinyint', { name: 'displayorder', width: 1, default: () => "'0'" })
  displayorder: boolean;

  @Column('tinyint', { name: 'highlight', width: 1, default: () => "'0'" })
  highlight: boolean;

  @Column('tinyint', { name: 'digest', width: 1, default: () => "'0'" })
  digest: boolean;

  @Column('tinyint', { name: 'rate', width: 1, default: () => "'0'" })
  rate: boolean;

  @Column('tinyint', { name: 'special', width: 1, default: () => "'0'" })
  special: boolean;

  @Column('tinyint', { name: 'attachment', width: 1, default: () => "'0'" })
  attachment: boolean;

  @Column('tinyint', { name: 'moderated', width: 1, default: () => "'0'" })
  moderated: boolean;

  @Column('int', { name: 'closed', unsigned: true, default: () => "'0'" })
  closed: number;

  @Column('tinyint', {
    name: 'stickreply',
    unsigned: true,
    default: () => "'0'",
  })
  stickreply: number;

  @Column('smallint', { name: 'recommends', default: () => "'0'" })
  recommends: number;

  @Column('smallint', { name: 'recommend_add', default: () => "'0'" })
  recommend_add: number;

  @Column('smallint', { name: 'recommend_sub', default: () => "'0'" })
  recommend_sub: number;

  @Column('int', { name: 'heats', unsigned: true, default: () => "'0'" })
  heats: number;

  @Column('smallint', { name: 'status', unsigned: true, default: () => "'0'" })
  status: number;

  @Column('tinyint', { name: 'isgroup', width: 1, default: () => "'0'" })
  isgroup: boolean;

  @Column('int', { name: 'favtimes', default: () => "'0'" })
  favtimes: number;

  @Column('int', { name: 'sharetimes', default: () => "'0'" })
  sharetimes: number;

  @Column('tinyint', { name: 'stamp', default: () => "'-1'" })
  stamp: number;

  @Column('tinyint', { name: 'icon', default: () => "'-1'" })
  icon: number;

  @Column('int', { name: 'pushedaid', default: () => "'0'" })
  pushedaid: number;

  @Column('smallint', { name: 'cover', default: () => "'0'" })
  cover: number;

  @Column('smallint', { name: 'replycredit', default: () => "'0'" })
  replycredit: number;

  @Column('char', { name: 'relatebytag', length: 255, default: () => "'0'" })
  relatebytag: string;

  @Column('int', { name: 'maxposition', unsigned: true, default: () => "'0'" })
  maxposition: number;

  @Column('char', { name: 'bgcolor', length: 8, default: () => "''" })
  bgcolor: string;

  @Column('int', { name: 'comments', unsigned: true, default: () => "'0'" })
  comments: number;

  @Column('smallint', { name: 'hidden', unsigned: true, default: () => "'0'" })
  hidden: number;

  @Column('int', { name: 'oldtypeid', unsigned: true })
  oldtypeid: number;
}
