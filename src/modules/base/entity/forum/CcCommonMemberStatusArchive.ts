import { Column, Index, Entity } from 'typeorm';

@Index('lastactivity', ['lastactivity', 'invisible'], {})
@Entity('cc_common_member_status_archive', { synchronize: false })
export class CcCommonMemberStatusArchive {
  @Column('int', { primary: true, name: 'uid', unsigned: true })
  uid: number;

  @Column('char', { name: 'regip', length: 15, default: () => "''" })
  regip: string;

  @Column('char', { name: 'lastip', length: 15, default: () => "''" })
  lastip: string;

  @Column('int', { name: 'lastvisit', unsigned: true, default: () => "'0'" })
  lastvisit: number;

  @Column('int', { name: 'lastactivity', unsigned: true, default: () => "'0'" })
  lastactivity: number;

  @Column('int', { name: 'lastpost', unsigned: true, default: () => "'0'" })
  lastpost: number;

  @Column('int', { name: 'lastsendmail', unsigned: true, default: () => "'0'" })
  lastsendmail: number;

  @Column('tinyint', { name: 'invisible', width: 1, default: () => "'0'" })
  invisible: boolean;

  @Column('smallint', { name: 'buyercredit', default: () => "'0'" })
  buyercredit: number;

  @Column('smallint', { name: 'sellercredit', default: () => "'0'" })
  sellercredit: number;

  @Column('int', {
    name: 'favtimes',
    unsigned: true,
    default: () => "'0'",
  })
  favtimes: number;

  @Column('int', {
    name: 'sharetimes',
    unsigned: true,
    default: () => "'0'",
  })
  sharetimes: number;

  @Column('tinyint', {
    name: 'profileprogress',
    unsigned: true,
    default: () => "'0'",
  })
  profileprogress: number;
}
