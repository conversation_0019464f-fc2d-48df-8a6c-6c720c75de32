import { Column, Index, Entity, PrimaryGeneratedColumn } from 'typeorm';

@Index('idtype', ['id', 'idtype'], {})
@Index('uid', ['uid', 'idtype', 'dateline'], {})
@Entity('cc_home_favorite', { synchronize: false })
export class CcHomeFavorite {
  @PrimaryGeneratedColumn({ type: 'int', name: 'favid', unsigned: true })
  favid: number;

  @Column('int', { name: 'uid', unsigned: true, default: () => "'0'" })
  uid: number;

  @Column('int', { name: 'id', unsigned: true, default: () => "'0'" })
  id: number;

  @Column('varchar', { name: 'idtype', length: 255, default: () => "''" })
  idtype: string;

  @Column('int', {
    name: 'spaceuid',
    unsigned: true,
    default: () => "'0'",
  })
  spaceuid: number;

  @Column('varchar', { name: 'title', length: 255, default: () => "''" })
  title: string;

  @Column('text', { name: 'description' })
  description: string;

  @Column('int', { name: 'dateline', unsigned: true, default: () => "'0'" })
  dateline: number;
}
