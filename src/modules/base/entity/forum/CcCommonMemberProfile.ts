import { Column, Entity } from 'typeorm';

@Entity('cc_common_member_profile', { synchronize: false })
export class CcCommonMemberProfile {
  @Column('int', { primary: true, name: 'uid', unsigned: true })
  uid: number;

  @Column('varchar', { name: 'realname', length: 255, default: () => "''" })
  realname: string;

  @Column('int', { name: 'gender', default: () => "'0'" })
  gender: number;

  @Column('int', { name: 'birthyear', unsigned: true, default: () => "'0'" })
  birthyear: number;

  @Column('int', { name: 'birthmonth', unsigned: true, default: () => "'0'" })
  birthmonth: number;

  @Column('int', { name: 'birthday', unsigned: true, default: () => "'0'" })
  birthday: number;

  @Column('varchar', {
    name: 'constellation',
    length: 255,
    default: () => "''",
  })
  constellation: string;

  @Column('varchar', { name: 'zodiac', length: 255, default: () => "''" })
  zodiac: string;

  @Column('varchar', { name: 'telephone', length: 255, default: () => "''" })
  telephone: string;

  @Column('varchar', { name: 'mobile', length: 255, default: () => "''" })
  mobile: string;

  @Column('varchar', { name: 'idcardtype', length: 255, default: () => "''" })
  idcardtype: string;

  @Column('varchar', { name: 'idcard', length: 255, default: () => "''" })
  idcard: string;

  @Column('varchar', { name: 'address', length: 255, default: () => "''" })
  address: string;

  @Column('varchar', { name: 'zipcode', length: 255, default: () => "''" })
  zipcode: string;

  @Column('varchar', { name: 'nationality', length: 255, default: () => "''" })
  nationality: string;

  @Column('varchar', {
    name: 'birthprovince',
    length: 255,
    default: () => "''",
  })
  birthprovince: string;

  @Column('varchar', { name: 'birthcity', length: 255, default: () => "''" })
  birthcity: string;

  @Column('varchar', { name: 'birthdist', length: 20, default: () => "''" })
  birthdist: string;

  @Column('varchar', {
    name: 'birthcommunity',
    length: 255,
    default: () => "''",
  })
  birthcommunity: string;

  @Column('varchar', {
    name: 'resideprovince',
    length: 255,
    default: () => "''",
  })
  resideprovince: string;

  @Column('varchar', { name: 'residecity', length: 255, default: () => "''" })
  residecity: string;

  @Column('varchar', { name: 'residedist', length: 20, default: () => "''" })
  residedist: string;

  @Column('varchar', {
    name: 'residecommunity',
    length: 255,
    default: () => "''",
  })
  residecommunity: string;

  @Column('varchar', { name: 'residesuite', length: 255, default: () => "''" })
  residesuite: string;

  @Column('varchar', {
    name: 'graduateschool',
    length: 255,
    default: () => "''",
  })
  graduateschool: string;

  @Column('varchar', { name: 'company', length: 255, default: () => "''" })
  company: string;

  @Column('varchar', { name: 'education', length: 255, default: () => "''" })
  education: string;

  @Column('varchar', { name: 'occupation', length: 255, default: () => "''" })
  occupation: string;

  @Column('varchar', { name: 'position', length: 255, default: () => "''" })
  position: string;

  @Column('varchar', { name: 'revenue', length: 255, default: () => "''" })
  revenue: string;

  @Column('varchar', {
    name: 'affectivestatus',
    length: 255,
    default: () => "''",
  })
  affectivestatus: string;

  @Column('varchar', { name: 'lookingfor', length: 255, default: () => "''" })
  lookingfor: string;

  @Column('varchar', { name: 'bloodtype', length: 255, default: () => "''" })
  bloodtype: string;

  @Column('varchar', { name: 'height', length: 255, default: () => "''" })
  height: string;

  @Column('varchar', { name: 'weight', length: 255, default: () => "''" })
  weight: string;

  @Column('varchar', { name: 'alipay', length: 255, default: () => "''" })
  alipay: string;

  @Column('varchar', { name: 'icq', length: 255, default: () => "''" })
  icq: string;

  @Column('varchar', { name: 'qq', length: 255, default: () => "''" })
  qq: string;

  @Column('varchar', { name: 'yahoo', length: 255, default: () => "''" })
  yahoo: string;

  @Column('varchar', { name: 'msn', length: 255, default: () => "''" })
  msn: string;

  @Column('varchar', { name: 'taobao', length: 255, default: () => "''" })
  taobao: string;

  @Column('varchar', { name: 'site', length: 255, default: () => "''" })
  site: string;

  @Column('text', { name: 'bio' })
  bio: string;

  @Column('text', { name: 'interest' })
  interest: string;

  @Column('text', { name: 'field1' })
  field1: string;

  @Column('text', { name: 'field2' })
  field2: string;

  @Column('text', { name: 'field3' })
  field3: string;

  @Column('text', { name: 'field4' })
  field4: string;

  @Column('text', { name: 'field5' })
  field5: string;

  @Column('text', { name: 'field6' })
  field6: string;

  @Column('text', { name: 'field7' })
  field7: string;

  @Column('text', { name: 'field8' })
  field8: string;
}
