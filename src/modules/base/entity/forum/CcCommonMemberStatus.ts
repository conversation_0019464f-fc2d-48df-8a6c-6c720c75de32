import { Column, Entity } from 'typeorm';

@Entity('cc_common_member_status', { synchronize: false })
export class CcCommonMemberStatus {
  @Column('int', { primary: true, name: 'uid', unsigned: true })
  uid: number;

  @Column('char', { name: 'regip', length: 15, default: () => "''" })
  regip: string;

  @Column('char', { name: 'lastip', length: 15, default: () => "''" })
  lastip: string;

  @Column('int', { name: 'port', unsigned: true, default: () => "'0'" })
  port: number;

  @Column('int', { name: 'lastvisit', unsigned: true, default: () => "'0'" })
  lastvisit: number;

  @Column('int', { name: 'lastactivity', unsigned: true, default: () => "'0'" })
  lastactivity: number;

  @Column('int', { name: 'lastpost', unsigned: true, default: () => "'0'" })
  lastpost: number;

  @Column('int', { name: 'lastsendmail', unsigned: true, default: () => "'0'" })
  lastsendmail: number;

  @Column('int', { name: 'invisible', default: () => "'0'" })
  invisible: number;

  @Column('int', { name: 'buyercredit', default: () => "'0'" })
  buyercredit: number;

  @Column('int', { name: 'sellercredit', default: () => "'0'" })
  sellercredit: number;

  @Column('int', { name: 'favtimes', unsigned: true, default: () => "'0'" })
  favtimes: number;

  @Column('int', { name: 'sharetimes', unsigned: true, default: () => "'0'" })
  sharetimes: number;

  @Column('int', {
    name: 'profileprogress',
    unsigned: true,
    default: () => "'0'",
  })
  profileprogress: number;
}
