import { Column, Entity } from 'typeorm';

@Entity('cc_common_member_field_forum', { synchronize: false })
export class CcCommonMemberFieldForum {
  @Column('int', { primary: true, name: 'uid', unsigned: true })
  uid: number;

  @Column('int', { name: 'publishfeed', default: () => "'0'" })
  publishfeed: number;

  @Column('int', { name: 'customshow', unsigned: true, default: () => "'26'" })
  customshow: number;

  @Column('varchar', { name: 'customstatus', length: 30, default: () => "''" })
  customstatus: string;

  @Column('text', { name: 'medals' })
  medals: string;

  @Column('text', { name: 'sightml' })
  sightml: string;

  @Column('text', { name: 'groupterms' })
  groupterms: string;

  @Column('varchar', { name: 'authstr', length: 20, default: () => "''" })
  authstr: string;

  @Column('text', { name: 'groups' })
  groups: string;

  @Column('varchar', {
    name: 'attentiongroup',
    length: 255,
    default: () => "''",
  })
  attentiongroup: string;
}
