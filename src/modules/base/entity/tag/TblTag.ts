import { Column, PrimaryGeneratedColumn, Entity } from "typeorm";

@Entity('tbl_tag', { synchronize: false })
export class TblTag {
  @PrimaryGeneratedColumn({ type: "bigint", name: "id", unsigned: true })
  id: number;

  @Column({        
    length: 100,
    default: () => "''",
  })
  name: string | null;

  @Column({        
    length: 32,
    default: () => "''",
  })
  synonym_id: string | null;

  @Column({
    width: 1,
    default: () => "'0'",
  })
  main: boolean | null;

  @Column({    
    default: () => "'0'",
  })
  property: number | null;

  @Column({
    length: 10,
    default: () => "''",
  })
  order: string | null;

  @Column({ name: "created_at" })
  created_at: Date;

  @Column({ name: "updated_at" })
  updated_at: Date;

  @Column({ name: "deleted_at", nullable: true })
  deleted_at: Date | null;
}
