import { Column, PrimaryGeneratedColumn, Entity } from "typeorm";

@Entity("WaitingArticle", { synchronize: false })
export class WaitingArticle {
  @PrimaryGeneratedColumn({ type: "bigint", name: "ID" })
  ID: string;

  @Column("bigint", { name: "ThreadID" })
  ThreadID: string;

  @Column("nvarchar", { name: "Url", length: 500 })
  Url: string;

  @Column("text", { name: "Remark", nullable: true })
  Remark: string | null;

  @Column("int", { name: "Status" })
  Status: number;

  @Column("datetime", { name: "CreateTime" })
  CreateTime: Date;

  @Column("bit", { name: "IsDeleted" })
  IsDeleted: boolean;
}
