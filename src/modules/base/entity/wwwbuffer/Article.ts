import { Column, PrimaryGeneratedColumn, Entity } from "typeorm";

@Entity("Article", { synchronize: false })
export class Article {
  @PrimaryGeneratedColumn({ type: "bigint", name: "ID" })
  ID: string;

  @Column("nvarchar", { name: "Url", length: 500 })
  Url: string;

  @Column("nvarchar", { name: "Title", length: 500 })
  Title: string;

  @Column("nvarchar", { name: "Board", nullable: true, length: 50 })
  Board: string | null;

  @Column("nvarchar", { name: "Author", nullable: true, length: 50 })
  Author: string | null;

  @Column("nvarchar", { name: "Channel", nullable: true, length: 500 })
  Channel: string | null;

  @Column("nvarchar", { name: "Tag", nullable: true, length: 50 })
  Tag: string | null;

  @Column("text", { name: "Content" })
  Content: string;

  @Column("text", { name: "RefContent", nullable: true })
  RefContent: string | null;

  @Column("nvarchar", { name: "AddedBy", nullable: true, length: 50 })
  AddedBy: string | null;

  @Column("nvarchar", { name: "ApprovedBy", nullable: true, length: 50 })
  ApprovedBy: string | null;

  @Column("datetime", { name: "CreateTime" })
  CreateTime: Date;

  @Column("datetime", { name: "ApprovedTime", nullable: true })
  ApprovedTime: Date | null;

  @Column("int", { name: "WWWArticleID", nullable: true })
  WWWArticleID: number | null;

  @Column("bit", { name: "FixNews", nullable: true })
  FixNews: boolean | null;

  @Column("bit", { name: "IsDeleted" })
  IsDeleted: boolean;

  @Column("bigint", { name: "WAID", nullable: true })
  WAID: string | null;
}
