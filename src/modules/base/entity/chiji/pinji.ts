import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';

@Entity('tbl_pinji', { synchronize: false })
export class Pinji {
  @PrimaryGeneratedColumn({ type: 'bigint', name: 'id' })
  id: string;

  @Column('varchar', { name: 'subject', length: 1000 })
  subject: string;

  @Column('text', { name: 'content' })
  content: string;

  @Column('varchar', { name: 'explain', length: 4000, default: () => "''" })
  explain: string;

  @Column('int', { name: 'type' })
  type: number;

  @Column('int', { name: 'complete', default: () => "'0'" })
  complete: number;

  @Column('varchar', {
    name: 'keywords',
    nullable: true,
    length: 1000,
    default: () => "''",
  })
  keywords: string | null;

  @Column('varchar', {
    name: 'nid',
    nullable: true,
    length: 1000,
    default: () => "''",
  })
  nid: string | null;

  @Column('datetime', { name: 'created_at', nullable: true })
  created_at: Date | null;

  @Column('datetime', { name: 'updated_at', nullable: true })
  updated_at: Date | null;

  @Column('datetime', { name: 'deleted_at', nullable: true })
  deleted_at: Date | null;
}
