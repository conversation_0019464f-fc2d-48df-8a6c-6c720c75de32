import { Column, Index, Entity, ManyToOne } from 'typeorm';

import { BaseEntity } from '@cool-midway/core';
import { ChatgptSessionDetailEntity } from './chatgptSessionDetail';

/**
 * Chatgpt Session Upload
 */
@Entity('chatgpt_session_upload', { synchronize: false })
export class ChatgptSessionUploadEntity extends BaseEntity {
  @Index()
  @Column({ comment: 'DetailID', default: 0 })
  detailId: number;

  @Column({ comment: 'Url', default: '' })
  url: string;

  @ManyToOne(() => ChatgptSessionDetailEntity, detail => detail.uploads, {
    createForeignKeyConstraints: false,
  })
  detail: ChatgptSessionDetailEntity;
}
