import { Column, Index, Entity } from 'typeorm';

import { BaseEntity } from '@cool-midway/core';

/**
 * Chatgpt Session
 */
@Entity('chatgpt_session', { synchronize: false })
export class ChatgptSessionEntity extends BaseEntity {
  @Index()
  @Column({ comment: 'UID', default: 0 })
  uid: number;

  @Column({ comment: '标题', default: '' })
  subject: string;

  @Column({ comment: '消息ID', default: '' })
  lastMessageId: string;

  @Column({ comment: '状态', default: 0 })
  status: number;

  @Column({ comment: 'Model', default: 0 })
  model: number;

  @Column({ comment: '人声类型', default: '' })
  voiceVal: string;

  @Column({ comment: '最后一条消息', default: '' })
  lasMsgText: string;

  @Column({ comment: '最后一条消息时间', default: 0 })
  lastMsgTime: number;

  @Column({ comment: '置顶', default: 0 })
  digest: number;
}
