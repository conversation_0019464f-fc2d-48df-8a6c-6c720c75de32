import { Column, Index, Entity, OneToMany } from 'typeorm';

import { BaseEntity } from '@cool-midway/core';
import { ChatgptSessionUploadEntity } from './chatgptSessionUpload';

/**
 * Chatgpt Session Detail
 */
@Entity('chatgpt_session_detail', { synchronize: false })
export class ChatgptSessionDetailEntity extends BaseEntity {
  @Index()
  @Column({ comment: 'UID', default: 0 })
  uid: number;

  @Index()
  @Column({ comment: '会话ID', default: 0 })
  sessionId: number;

  @Index()
  @Column({ comment: '消息ID', default: '' })
  messageId: string;

  // 0:问题 1:回答
  @Column({ comment: '类型', default: 0 })
  type: number;

  @Column({ comment: '内容', type: 'text', default: '' })
  content: string;

  @OneToMany(() => ChatgptSessionUploadEntity, upload => upload.detail, {
    createForeignKeyConstraints: false,
  })
  uploads: ChatgptSessionUploadEntity[];
}
