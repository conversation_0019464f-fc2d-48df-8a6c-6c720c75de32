import { Column, Entity } from 'typeorm';

import { BaseEntity } from '@cool-midway/core';

/**
 * AI Vender Model
 */
@Entity('ai_vender_model', { synchronize: false })
export class AiVenderModelEntity extends BaseEntity {
  @Column({ comment: '供应商ID', default: 0 })
  venderId: number;

  @Column({ comment: '名称', default: '' })
  name: string;

  @Column({ comment: '模型', default: '' })
  model: string;

  @Column({ comment: '类型', default: '' })
  type: string;

  @Column({ comment: '排序', default: '' })
  order: string;

  @Column({ comment: '权限', default: '' })
  perm: string;
}
