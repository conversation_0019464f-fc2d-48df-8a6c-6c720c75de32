import { Column, Index, Entity } from 'typeorm';

import { BaseEntity } from '@cool-midway/core';

/**
 * ChatGPT User Tag
 */
@Entity('chatgpt_user_tag', { synchronize: false })
export class ChatgptUserTagEntity extends BaseEntity {
  @Index()
  @Column({ comment: 'UID', default: 0 })
  uid: number;

  @Column({ comment: 'SessionID', default: 0 })
  sessionid: number;

  @Column({ comment: 'TagID', default: 0 })
  tagid: number;
}
