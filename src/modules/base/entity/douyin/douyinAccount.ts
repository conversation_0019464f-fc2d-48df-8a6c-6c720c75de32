import { Column, Entity } from 'typeorm';

import { BaseEntity } from '@cool-midway/core';

/**
 * Do<PERSON>in Account
 */
@Entity('douyin_account', { synchronize: false })
export class DouyinAccountEntity extends BaseEntity {
  @Column({ comment: 'username', default: '' })
  username: string;

  @Column({ type: 'text', comment: '<PERSON><PERSON>', default: '' })
  cookie: string;

  @Column({ comment: 'Cookie过期时间', default: '' })
  exp: string;

  @Column({ comment: '状态', default: 1 })
  status: number;

  @Column({ comment: '主号', default: 0 })
  master: number;
}
