import { Column, Index, Entity } from 'typeorm';

import { BaseEntity } from '@cool-midway/core';

/**
 * Douyin Tag User
 */
@Entity('douyin_tag_user', { synchronize: false })
export class DouyinTagUserEntity extends BaseEntity {
  @Index()
  @Column({ comment: 'uid', default: 0 })
  uid: number;

  @Index()
  @Column({ comment: 'tagId', default: 0 })
  tagId: number;

  @Column({ comment: 'followId', default: 0 })
  followId: number;
}
