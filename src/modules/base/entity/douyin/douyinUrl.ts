import { Column, Index, Entity } from 'typeorm';

import { BaseEntity } from '@cool-midway/core';

/**
 * Douyin Url
 */
@Entity('douyin_url', { synchronize: false })
export class DouyinUrlEntity extends BaseEntity {
  @Index()
  @Column({ comment: 'UID', default: 0 })
  uid: number;

  @Index()
  @Column({ comment: 'Url', default: '' })
  url: string;

  @Index()
  @Column({ comment: '标题', default: '' })
  title: string;

  @Column({ comment: '作者', default: '' })
  author: string;

  @Index()
  @Column({ comment: '视频创建时间', default: 0 })
  videoDate: number;

  @Column({ comment: '目标Url', default: '', length: 1000 })
  targetUrl: string;

  @Column({ comment: '本地文件', default: '' })
  localFile: string;

  @Column({ comment: '本地封面Url', default: '' })
  localCover: string;

  @Column({ comment: '远程封面Url', default: '', length: 500 })
  remoteCoverUrl: string;

  @Column({ comment: '远程文件路径', default: '' })
  remoteFilePath: string;

  @Column({ comment: '远程文件Url', default: '' })
  remoteFileUrl: string;

  @Column({ comment: '类型', default: '' })
  type: string;

  @Index()
  @Column({ comment: 'tingwu transId', default: '' })
  transId: string;

  @Column({ type: 'text', comment: '文本', default: '' })
  txt: string;

  //1 已处理 0 待处理 -2 处理异常 -3 删除
  @Index()
  @Column({ comment: '状态', default: 0 })
  status: number;

  @Index()
  @Column({ comment: '跟踪ID', default: 0 })
  followId: number;

  @Column({ comment: '立即执行', default: 0 })
  run: number;

  @Column({ comment: '错误信息', default: '' })
  message: string;
}
