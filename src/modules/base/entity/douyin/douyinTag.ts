import { Column, Index, Entity } from 'typeorm';

import { BaseEntity } from '@cool-midway/core';

/**
 * Douyin Tag
 */
@Entity('douyin_tag', { synchronize: false })
export class DouyinTagEntity extends BaseEntity {
  @Index()
  @Column({ comment: 'uid', default: 0 })
  uid: number;

  @Column({ comment: 'name', default: '' })
  name: string;

  @Column({ comment: '展示标签', default: 1 })
  show: number;
}
