import { Column, Index, Entity } from 'typeorm';

import { BaseEntity } from '@cool-midway/core';

/**
 * Douyin Follow
 */
@Entity('douyin_follow', { synchronize: false })
export class DouyinFollowEntity extends BaseEntity {
  @Index()
  @Column({ comment: 'UID', default: 0 })
  uid: number;

  @Index()
  @Column({ comment: 'dyUid', default: '' })
  dyUid: string;

  @Column({ comment: 'avatar', default: '' })
  avatar: string;

  @Index()
  @Column({ comment: '作者', default: '' })
  author: string;

  @Column({ comment: '下载以前', default: 0 })
  before: number;

  @Column({ comment: '盯住以后', default: 0 })
  after: number;
  //0: 正常 -1: 初始化 -2: 已禁用 -3: 删除
  @Column({ comment: '状态', default: -1 })
  status: number;

  @Column({ comment: '已完成', default: 0 })
  finish: number;

  @Column({ comment: '总文件数量', default: 0 })
  total: number;

  @Column({ comment: '执行间隔', default: 60 })
  interval: number;

  @Column({ comment: '最新视频发布时间', default: 0 })
  lastVideoDate: number;

  @Column({ comment: '上次执行时间', default: 0 })
  lastRunAt: number;

  @Column({ comment: '错误信息', default: '' })
  message: string;
}
