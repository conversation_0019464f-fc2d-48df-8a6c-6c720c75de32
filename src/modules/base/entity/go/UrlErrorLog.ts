import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';

@Entity('UrlErrorLog', { synchronize: false })
export class UrlErrorLog {
  @PrimaryGeneratedColumn()
  ID: number;

  @Column({ type: 'varchar', length: 50, nullable: true })
  ForumName: string;

  @Column({ type: 'varchar', length: 500 })
  OriginalUrl: string;

  @Column({ type: 'varchar', length: 500 })
  Referrer: string;

  @Column({ type: 'char', length: 15 })
  IPAddress: string;

  @Column({ type: 'varchar', length: 50 })
  BrowserType: string;

  @Column({ type: 'varchar', length: 50 })
  Platform: string;

  @Column({ type: 'varchar', length: 50, nullable: true })
  Resolution: string;

  @Column('bigint')
  VisitTime: number;

  @Column({
    type: 'bit',
    transformer: {
      to(value: boolean): number {
        return value ? 1 : 0;
      },
      from(value: Buffer): boolean {
        return value[0] === 1;
      },
    },
  })
  IsDeleted: boolean;
}
