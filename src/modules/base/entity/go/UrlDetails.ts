import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';

@Entity('UrlDetails', { synchronize: false })
export class UrlDetails {
  @PrimaryGeneratedColumn()
  ID: number;

  @Column({ type: 'varchar', length: 50, nullable: true })
  ForumName: string;

  @Column({ type: 'varchar', length: 500 })
  OriginalUrl: string;

  @Column({ type: 'varchar', length: 3000, nullable: true })
  TargetUrl: string;

  @Column({ type: 'varchar', length: 500 })
  Referrer: string;

  @Column({ type: 'varchar', length: 50, nullable: true })
  Query: string;

  @Column({ type: 'char', length: 15 })
  IPAddress: string;

  @Column({ type: 'varchar', length: 50 })
  BrowserType: string;

  @Column({ type: 'varchar', length: 50 })
  Platform: string;

  @Column({ type: 'varchar', length: 50 })
  Year: string;

  @Column({ type: 'varchar', length: 50 })
  Month: string;

  @Column({ type: 'varchar', length: 50 })
  Week: string;

  @Column({ type: 'varchar', length: 50 })
  Date: string;

  @Column({ type: 'varchar', length: 50 })
  VisitTime: string;

  @Column({
    type: 'bit',
    transformer: {
      to(value: boolean): number {
        return value ? 1 : 0;
      },
      from(value: Buffer): boolean {
        return value[0] === 1;
      },
    },
  })
  IsDeleted: boolean;

  @Column({ type: 'varchar', length: 50, nullable: true })
  Resolution: string;
}
