import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';

@Entity('Url', { synchronize: false })
export class Url {
  @PrimaryGeneratedColumn()
  ID: number;

  @Column('bigint')
  FatherID: number;

  @Column({ type: 'varchar', length: 255, nullable: true })
  FatherPath: string;

  @Column({
    type: 'bit',
    transformer: {
      to(value: boolean): number {
        return value ? 1 : 0;
      },
      from(value: Buffer): boolean {
        return value[0] === 1;
      },
    },
  })
  leaf: boolean;

  @Column({ type: 'varchar', length: 255, nullable: true })
  iconCls: string;

  @Column({ type: 'varchar', length: 255 })
  OriginalUrl: string;

  @Column({ type: 'varchar', length: 255, nullable: true })
  OriginalUrlPath: string;

  @Column({ type: 'varchar', length: 255, nullable: true })
  TargetUrl: string;

  @Column('datetime')
  CreateTime: Date;

  @Column({
    type: 'bit',
    transformer: {
      to(value: boolean): number {
        return value ? 1 : 0;
      },
      from(value: Buffer): boolean {
        return value[0] === 1;
      },
    },
  })
  IsDeleted: boolean;

  @Column({ type: 'varchar', length: 255, nullable: true })
  Describe: string;

  @Column({
    type: 'bit',
    transformer: {
      to(value: boolean): number {
        return value ? 1 : 0;
      },
      from(value: boolean): boolean {
        return value;
      },
    },
  })
  Enable: boolean;
}
