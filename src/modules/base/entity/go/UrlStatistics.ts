import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';

@Entity('UrlStatistics', { synchronize: false })
export class UrlStatistics {
  @PrimaryGeneratedColumn()
  ID: number;

  @Column({ type: 'varchar', length: 500 })
  OriginalUrl: string;

  @Column('bigint')
  Hits: number;

  @Column({ type: 'varchar', length: 50 })
  Year: string;

  @Column({ type: 'varchar', length: 50 })
  Month: string;

  @Column({ type: 'varchar', length: 50 })
  Week: string;

  @Column({ type: 'varchar', length: 50 })
  CreateTime: string;

  @Column({
    type: 'bit',
    transformer: {
      to(value: boolean): number {
        return value ? 1 : 0;
      },
      from(value: Buffer): boolean {
        return value[0] === 1;
      },
    },
  })
  IsDeleted: boolean;
}
