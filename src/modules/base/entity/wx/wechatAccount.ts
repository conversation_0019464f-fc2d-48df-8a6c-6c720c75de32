import { Column, Index, Entity } from 'typeorm';
import { BaseEntity } from '@cool-midway/core';

/**
 * Wechat Account
 */
@Entity('wechat_account', { synchronize: false })
export class WechatAccountEntity extends BaseEntity {
  @Index()
  @Column({ comment: '微信ID', default: '' })
  wxId: string;

  @Column({ comment: '微信昵称', default: '' })
  wxNickname: string;

  @Column({ comment: '头像', default: '' })
  avatar: string;

  @Column({ comment: '二维码', default: '' })
  qrCode: string;

  @Column({ comment: '类型', default: 0 })
  type: number;

  @Column({ comment: '状态', default: 0 })
  status: number;

  @Column({ comment: '附件', default: '' })
  errorMessage: string;
}
