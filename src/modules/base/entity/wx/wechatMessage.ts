import { Column, Index, Entity } from 'typeorm';
import { BaseEntity } from '@cool-midway/core';

/**
 * Wechat Message
 */
@Entity('wechat_message', { synchronize: false })
export class WechatMessageEntity extends BaseEntity {
  @Column({ comment: 'TID', default: 0, unsigned: true })
  tid: number;

  @Column({ comment: 'FID', default: 0, unsigned: true })
  fid: number;

  @Column({ comment: 'PID', default: 0, unsigned: true })
  pid: number;

  @Index()
  @Column({ comment: 'GroupId', default: 0, unsigned: true })
  groupId: number;

  @Index()
  @Column({ comment: '群ID', default: '' })
  roomId: string;

  @Column({ comment: '群名称', default: '' })
  topic: string;

  @Column({ comment: '头像', default: '' })
  avatar: string;

  @Index()
  @Column({ comment: '微信ID', default: '' })
  wxId: string;

  @Column({ comment: '微信昵称', default: '' })
  wxNickname: string;

  @Column({ comment: '文本', default: '', type: 'text' })
  text: string;

  @Column({ comment: '类型', default: 0, unsigned: true })
  type: number;

  @Column({ comment: '文件', default: '' })
  filename: string;

  @Column({ comment: '文件大小', default: 0, unsigned: true })
  filesize: number;

  @Column({ comment: '附件', default: '' })
  attachment: string;

  @Column({ comment: 'width', default: 0, unsigned: true })
  width: number;

  @Column({ comment: 'height', default: 0, unsigned: true })
  height: number;

  @Column({ comment: '是否缩列图', default: 0, unsigned: true })
  thumb: number;

  @Column({ comment: 'height', default: 0, unsigned: true })
  thumbwidth: number;

  @Column({ comment: 'height', default: 0, unsigned: true })
  thumbheight: number;

  @Column({ comment: '状态', default: 0 })
  status: number;

  @Column({ comment: '考题', default: 0 })
  exam: number;

  @Column({ comment: '附件', default: '' })
  errorMessage: string;
}
