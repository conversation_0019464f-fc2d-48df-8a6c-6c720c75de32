import { Column, Index, Unique, Entity } from 'typeorm';
import { BaseEntity } from '@cool-midway/core';

/**
 * Wechat Group
 */
@Entity('wechat_group', { synchronize: false })
export class WechatGroupEntity extends BaseEntity {
  @Unique('idx_roomId', ['roomId'])
  @Column({ comment: '群ID', default: '' })
  roomId: string;

  @Column({ comment: '群名称', default: '' })
  topic: string;

  @Index()
  @Column({ comment: '微信ID', default: '' })
  wxId: string;

  @Column({ comment: '微信昵称', default: '' })
  wxNickname: string;

  @Column({ comment: '头像', default: '' })
  avatar: string;

  @Column({ comment: '人数', default: 0, unsigned: true })
  memberCount: number;

  @Column({ comment: '状态', default: 0 })
  status: number;

  @Column({ comment: '最后一条消息', type: 'text', default: '' })
  lasMsgText: string;

  @Column({ comment: '最后一条消息时间', default: 0 })
  lastMsgTime: number;

  @Column({ comment: '置顶', default: 0 })
  digest: number;

  @Column({ comment: '附件', default: '' })
  errorMessage: string;
}
