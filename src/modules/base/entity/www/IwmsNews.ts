import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';

@Entity('iwms_news', { synchronize: false })
export class IwmsNews {
  @PrimaryGeneratedColumn({ type: 'int', name: 'articleid' })
  articleid: number;

  @Column('text', { name: 'content', nullable: true })
  content: string | null;

  @Column('nvarchar', { name: 'title', nullable: true, length: 200 })
  title: string | null;

  @Column('int', { name: 'classid', nullable: true, default: () => '(0)' })
  classid: number | null;

  @Column('nvarchar', { name: 'Nkey', nullable: true, length: 255 })
  Nkey: string | null;

  @Column('int', { name: 'hits', default: () => '(0)' })
  hits: number;

  @Column('datetime', {
    name: 'dateandtime',
    nullable: true,
    default: () => 'getdate()',
  })
  dateandtime: Date | null;

  @Column('int', { name: 'dayHits', nullable: true, default: () => '(0)' })
  dayHits: number | null;

  @Column('int', { name: 'weekHits', nullable: true, default: () => '(0)' })
  weekHits: number | null;

  @Column('int', { name: 'remarkNum', nullable: true, default: () => '(0)' })
  remarkNum: number | null;

  @Column('bit', { name: 'imgNews', default: () => '(0)' })
  imgNews: boolean;

  @Column('nvarchar', { name: 'titleImg', nullable: true, length: 200 })
  titleImg: string | null;

  @Column('int', { name: 'topicid', nullable: true, default: () => '(0)' })
  topicid: number | null;

  @Column('text', { name: 'summary', nullable: true })
  summary: string | null;

  @Column('bit', { name: 'highlight', default: () => '(0)' })
  highlight: boolean;

  @Column('nvarchar', { name: 'permitGroups', nullable: true, length: 255 })
  permitGroups: string | null;

  @Column('int', { name: 'memberid', nullable: true, default: () => '(0)' })
  memberid: number | null;

  @Column('int', { name: 'checkUserid', nullable: true, default: () => '(0)' })
  checkUserid: number | null;

  @Column('nvarchar', { name: 'aUrl', nullable: true, length: 255 })
  aUrl: string | null;

  @Column('nvarchar', { name: 'author', nullable: true, length: 50 })
  author: string | null;

  @Column('nvarchar', { name: 'source', nullable: true, length: 50 })
  source: string | null;

  @Column('nvarchar', { name: 'authorEmail', nullable: true, length: 100 })
  authorEmail: string | null;

  @Column('nvarchar', { name: 'sourceUrl', nullable: true, length: 255 })
  sourceUrl: string | null;

  @Column('bit', { name: 'remarkLink', nullable: true, default: () => '(0)' })
  remarkLink: boolean | null;

  @Column('bit', { name: 'allowRemark', nullable: true, default: () => '(1)' })
  allowRemark: boolean | null;

  @Column('bit', { name: 'outerImg', nullable: true, default: () => '(0)' })
  outerImg: boolean | null;

  @Column('int', { name: 'price', nullable: true, default: () => '(0)' })
  price: number | null;

  @Column('int', { name: 'markLimit', nullable: true, default: () => '(0)' })
  markLimit: number | null;

  @Column('text', { name: 'pageTitle', nullable: true })
  pageTitle: string | null;

  @Column('nvarchar', { name: 'pollIds', nullable: true, length: 255 })
  pollIds: string | null;

  @Column('int', { name: 'digg', nullable: true, default: () => '(0)' })
  digg: number | null;

  @Column('int', { name: 'diggWeek', nullable: true, default: () => '(0)' })
  diggWeek: number | null;

  @Column('nvarchar', { name: 'editor', nullable: true, length: 100 })
  editor: string | null;
}
