import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';

@Entity('iwms_class', { synchronize: false })
export class IwmsClass {
  @PrimaryGeneratedColumn({ type: 'int', name: 'classID' })
  classID: number;

  @Column('nvarchar', { name: 'class', nullable: true, length: 50 })
  class: string | null;

  @Column('int', { name: 'orderID', nullable: true, default: () => '(0)' })
  orderID: number | null;

  @Column('int', { name: 'articleNum', nullable: true, default: () => '(0)' })
  articleNum: number | null;

  @Column('int', { name: 'imgNewsNum', nullable: true, default: () => '(0)' })
  imgNewsNum: number | null;

  @Column('int', { name: 'listStyle', nullable: true, default: () => '(0)' })
  listStyle: number | null;

  @Column('text', { name: 'newsAd', nullable: true })
  newsAd: string | null;

  @Column('text', { name: 'headAd', nullable: true })
  headAd: string | null;

  @Column('text', { name: 'logo', nullable: true })
  logo: string | null;

  @Column('int', { name: 'styleid', nullable: true, default: () => '(0)' })
  styleid: number | null;

  @Column('datetime', {
    name: 'lastupdate',
    nullable: true,
    default: () => 'getdate()',
  })
  lastupdate: Date | null;

  @Column('int', { name: 'parentID', nullable: true, default: () => '(0)' })
  parentID: number | null;

  @Column('nvarchar', { name: 'pPath', nullable: true, length: 255 })
  pPath: string | null;

  @Column('int', { name: 'depth', nullable: true, default: () => '(0)' })
  depth: number | null;

  @Column('int', { name: 'child', nullable: true, default: () => '(0)' })
  child: number | null;

  @Column('bit', { name: 'canAdd', nullable: true, default: () => '(0)' })
  canAdd: boolean | null;

  @Column('int', { name: 'cTempid', nullable: true, default: () => '(0)' })
  cTempid: number | null;

  @Column('int', { name: 'newsTempID', nullable: true, default: () => '(0)' })
  newsTempID: number | null;

  @Column('nvarchar', { name: 'cUrl', nullable: true, length: 255 })
  cUrl: string | null;

  @Column('bit', { name: 'LastNode', nullable: true, default: () => '(0)' })
  LastNode: boolean | null;

  @Column('bit', { name: 'cInBar', nullable: true, default: () => '(0)' })
  cInBar: boolean | null;

  @Column('bit', { name: 'cInNav', nullable: true, default: () => '(0)' })
  cInNav: boolean | null;

  @Column('int', { name: 'cBindNum', nullable: true, default: () => '(0)' })
  cBindNum: number | null;

  @Column('nvarchar', { name: 'color', nullable: true, length: 10 })
  color: string | null;

  @Column('bit', { name: 'newWin', nullable: true, default: () => '(0)' })
  newWin: boolean | null;

  @Column('nvarchar', { name: 'allowGroups', nullable: true, length: 255 })
  allowGroups: string | null;

  @Column('int', { name: 'addMark', nullable: true, default: () => '(0)' })
  addMark: number | null;

  @Column('bit', { name: 'inLeft', nullable: true, default: () => '(0)' })
  inLeft: boolean | null;

  @Column('int', { name: 'pageSize', nullable: true, default: () => '(0)' })
  pageSize: number | null;

  @Column('nvarchar', { name: 'metaDescription', nullable: true, length: 255 })
  metaDescription: string | null;

  @Column('nvarchar', { name: 'sDir', nullable: true, length: 50 })
  sDir: string | null;

  @Column('nvarchar', { name: 'rssFeed', nullable: true, length: 255 })
  rssFeed: string | null;

  @Column('int', { name: 'staticPages', nullable: true, default: () => '(0)' })
  staticPages: number | null;

  @Column('text', { name: 'sideAd', nullable: true })
  sideAd: string | null;

  @Column('text', { name: 'topAd', nullable: true })
  topAd: string | null;

  @Column('text', { name: 'footAd', nullable: true })
  footAd: string | null;

  @Column('text', { name: 'newsSideAd', nullable: true })
  newsSideAd: string | null;

  @Column('text', { name: 'newsTopAd', nullable: true })
  newsTopAd: string | null;

  @Column('text', { name: 'newsFootAd', nullable: true })
  newsFootAd: string | null;

  @Column('int', { name: 'addVMoney', nullable: true, default: () => '(0)' })
  addVMoney: number | null;

  @Column('text', { name: 'ipLimited', nullable: true })
  ipLimited: string | null;

  @Column('bit', { name: 'ipLimit', nullable: true, default: () => '(0)' })
  ipLimit: boolean | null;

  @Column('int', { name: 'cType', nullable: true, default: () => '(0)' })
  cType: number | null;

  @Column('nvarchar', { name: 'cssInBar', nullable: true, length: 20 })
  cssInBar: string | null;

  @Column('nvarchar', { name: 'picFrame', nullable: true, length: 100 })
  picFrame: string | null;

  @Column('nvarchar', { name: 'cssFrame', nullable: true, length: 20 })
  cssFrame: string | null;

  @Column('nvarchar', { name: 'metaKeyword', nullable: true, length: 255 })
  metaKeyword: string | null;

  @Column('text', { name: 'headlineSummary', nullable: true })
  headlineSummary: string | null;

  @Column('nvarchar', { name: 'cBindSorts', nullable: true, length: 255 })
  cBindSorts: string | null;
}
