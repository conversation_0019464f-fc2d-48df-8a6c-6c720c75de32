import { Column, Entity } from 'typeorm';
import { BaseEntity } from '@cool-midway/core';

/**
 * Mail
 */
@Entity('common_mail', { synchronize: false })
export class MailEntity extends BaseEntity {
  @Column({ comment: 'From', default: '' })
  from: string;

  @Column({ comment: 'To', default: '' })
  to: string;

  @Column({ comment: '标题', default: '' })
  subject: string;

  @Column({ comment: '内容', type: 'text', default: '' })
  message: string;

  @Column({ comment: '状态', type: 'text', default: '' })
  status: string;

  @Column({ comment: 'IP', default: '' })
  ip: string;
}
