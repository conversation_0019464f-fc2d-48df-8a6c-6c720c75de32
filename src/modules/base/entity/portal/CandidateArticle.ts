import { Column, Entity } from 'typeorm';
import { BaseEntity } from '@cool-midway/core';

@Entity('portal_candidate_article', { synchronize: false })
export class PortalCandidateArticleEntity extends BaseEntity {
  @Column({ length: 500, default: '' })
  url: string;

  @Column({ length: 500, default: '' })
  title: string;

  @Column({ length: 50, nullable: true })
  board: string | null;

  @Column({ length: 50, nullable: true })
  author: string | null;

  @Column({ length: 500, nullable: true })
  channel: string | null;

  @Column({ length: 50, nullable: true })
  tag: string | null;

  @Column({ type: 'longtext', default: '' })
  content: string;

  @Column({ type: 'longtext', nullable: true })
  refContent: string | null;

  @Column({ length: 50, nullable: true })
  addedBy: string | null;

  @Column({ length: 50, nullable: true })
  approvedBy: string | null;

  @Column({ type: 'datetime', nullable: true })
  approvedTime: Date | null;

  @Column({ type: 'int', nullable: true })
  wwwArticleID: number | null;

  @Column({ type: 'boolean', nullable: true, default: false })
  fixNews: boolean | null;

  @Column({ type: 'bigint', nullable: true })
  waid: string | null;
}
