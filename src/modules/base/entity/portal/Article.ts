import { Column, Entity } from 'typeorm';
import { BaseEntity } from '@cool-midway/core';

@Entity('portal_article', { synchronize: false })
export class PortalArticleEntity extends BaseEntity {
  @Column({ length: 200, default: '' })
  pic: string;

  @Column({ default: 0 })
  type: number;

  @Column({ length: 200, default: '' })
  title: string;

  @Column({ type: 'text', default: '' })
  summary: string;

  @Column({ type: 'longtext', default: '' })
  content: string;
  
  @Column({ unsigned: true, default: 0 })
  datetime: number;

  @Column({ length: 50, default: '' })
  author: string;

  @Column({ unsigned: true, default: 0 })
  hits: number;

  @Column({ type: 'bigint', unsigned: true, default: 0 })
  forumTid: number;

  @Column({ length: 200, default: '' })
  category: string;

  @Column({ length: 500, default: '' })
  externalLink: string;

  @Column({ default: 0 })
  status: number;

  @Column({ default: 0 })
  displayOrder: number;

  @Column({ type: 'text', default: '' })
  tags: string;

  @Column({ type: 'text', default: '' })
  tagsVal: string;

  @Column({ type: 'text', default: '' })
  majorTags: string;

  @Column({ type: 'text', default: '' })
  majorTagsVal: string;

  @Column({ type: 'text', default: '' })
  schoolTags: string;

  @Column({ type: 'text', default: '' })
  schoolTagsVal: string;
}
