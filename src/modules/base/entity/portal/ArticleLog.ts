import { Column, Entity } from 'typeorm';
import { BaseEntity } from '@cool-midway/core';

@Entity('portal_article_log', { synchronize: false })
export class PortalArticleLogEntity extends BaseEntity {

  @Column({ unsigned: true, default: 0 })
  articleId: number;

  @Column({ unsigned: true, default: 0 })
  hitsDatetime: number;

  @Column({ unsigned: true, default: 0 })
  uid: number;

  @Column({ length: 50, default: '' })
  username: string;

  @Column({ unsigned: true, default: 0 })
  viewDatetime: number;
}
