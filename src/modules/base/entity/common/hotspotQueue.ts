import { Column, Entity } from 'typeorm';

import { BaseEntity } from '@cool-midway/core';

/**
 * Hotspot Queue
 */
@Entity('common_hotspot_queue', { synchronize: false })
export class CommonHotspotQueueEntity extends BaseEntity {
  @Column({ comment: '开始时间', default: 0 })
  begin: number;

  @Column({ comment: '目标地址', default: '', length: 1000 })
  targetUrl: string;

  @Column({ comment: 'Referer', default: '', type: 'text' })
  referer: string;

  @Column({ comment: '类型', default: 0 })
  type: number;

  @Column({ comment: 'pid', default: 0 })
  pid: number;
}
