import { Column, Entity } from 'typeorm';

import { BaseEntity } from '@cool-midway/core';

/**
 * Hotspot
 */
@Entity('common_hotspot', { synchronize: false })
export class CommonHotspotEntity extends BaseEntity {
  @Column({ comment: '开始时间', default: 0 })
  begin: number;

  @Column({ comment: '结束时间', default: 0 })
  end: number;

  @Column({ comment: '标题', default: '' })
  title: string;

  @Column({ comment: '目标地址', default: '', length: 1000 })
  targetUrl: string;

  @Column({ comment: 'Referer', default: '', type: 'text' })
  referer: string;

  @Column({ comment: '展示次数范围', default: 0 })
  showTimeMin: number;

  @Column({ comment: '展示次数范围', default: 0 })
  showTimeMax: number;

  @Column({ comment: '展示间隔范围 ', default: 0 })
  showIntervalMin: number;

  @Column({ comment: '展示间隔范围', default: 0 })
  showIntervalMax: number;

  @Column({ comment: '类型', default: 0 })
  type: number;

  @Column({ comment: '状态', default: 0 })
  status: number;
}
