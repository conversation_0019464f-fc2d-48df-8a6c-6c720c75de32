import { Column, Entity } from 'typeorm';

import { BaseEntity } from '@cool-midway/core';

/**
 * 搜狗
 */
@Entity('common_sougou', { synchronize: false })
export class CommonSougouEntity extends BaseEntity {
  @Column({ comment: '标题', type: 'text' })
  subject: string;

  @Column({ comment: '内容', type: 'text' })
  content: string;

  @Column({ comment: '类型' })
  type: string;

  @Column({ comment: '文件名' })
  filename: string;
}
