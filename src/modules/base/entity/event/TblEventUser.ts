import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';

@Entity('tbl_event_user', { synchronize: false })
export class TblEventUser {
  @PrimaryGeneratedColumn({ type: 'bigint', name: 'id', unsigned: true })
  id: string;

  @Column('varchar', { name: 'username', length: 200 })
  username: string;

  @Column('varchar', { name: 'nickname', length: 200, default: () => "''" })
  nickname: string;

  @Column('varchar', { name: 'password', length: 200 })
  password: string;

  @Column('int', {
    name: 'type',
    nullable: true,
    unsigned: true,
    default: () => "'0'",
  })
  type: number | null;

  @Column('int', {
    name: 'status',
    nullable: true,
    unsigned: true,
    default: () => "'0'",
  })
  status: number | null;

  @Column('datetime', { name: 'created_at', nullable: true })
  created_at: Date | null;
}
