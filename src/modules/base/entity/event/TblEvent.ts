import {
  Column,
  <PERSON>ti<PERSON>,
  PrimaryGeneratedColumn,
  OneToMany,
  OneToOne,
  JoinColumn,
} from 'typeorm';
import { TblEventLocation } from './TblEventLocation';
import { TblSchoolDic } from './TblSchoolDic';

@Entity('tbl_event', { synchronize: false })
export class TblEvent {
  @PrimaryGeneratedColumn({ type: 'bigint', name: 'id', unsigned: true })
  id: string;

  @Column('int', {
    name: 'school_id',
    nullable: true,
    unsigned: true,
    default: () => "'0'",
  })
  school_id: number | null;

  @Column('varchar', {
    name: 'subject',
    nullable: true,
    length: 255,
    default: () => "''",
  })
  subject: string | null;

  @Column('text', { name: 'content', nullable: true })
  content: string | null;

  @Column('int', {
    name: 'www_url',
    nullable: true,
    unsigned: true,
    default: () => "'0'",
  })
  www_url: number | null;

  @Column('int', {
    name: 'www_url_senduser',
    nullable: true,
    unsigned: true,
    default: () => "'0'",
  })
  www_url_senduser: number | null;

  @Column('int', {
    name: 'iese_url',
    nullable: true,
    unsigned: true,
    default: () => "'0'",
  })
  iese_url: number | null;

  @Column('int', {
    name: 'apply_url',
    nullable: true,
    unsigned: true,
    default: () => "'0'",
  })
  apply_url: number | null;

  @Column('int', {
    name: 'forum_url',
    nullable: true,
    unsigned: true,
    default: () => "'0'",
  })
  forum_url: number | null;

  @Column('int', {
    name: 'forum_url_fid',
    nullable: true,
    unsigned: true,
    default: () => "'0'",
  })
  forum_url_fid: number | null;

  @Column('int', {
    name: 'forum_url_pid',
    nullable: true,
    unsigned: true,
    default: () => "'0'",
  })
  forum_url_pid: number | null;

  @Column('int', {
    name: 'forum_url_typeid',
    nullable: true,
    unsigned: true,
    default: () => "'0'",
  })
  forum_url_typeid: number | null;

  @Column('int', {
    name: 'forum_url_senduser',
    nullable: true,
    unsigned: true,
    default: () => "'0'",
  })
  forum_url_senduser: number | null;

  @Column('int', {
    name: 'highlight',
    nullable: true,
    unsigned: true,
    default: () => "'0'",
  })
  highlight: number | null;

  @Column('int', {
    name: 'digest',
    nullable: true,
    unsigned: true,
    default: () => "'0'",
  })
  digest: number | null;

  @Column('int', {
    name: 'stick',
    nullable: true,
    unsigned: true,
    default: () => "'0'",
  })
  stick: number | null;

  @Column('tinyint', {
    name: 'push_0',
    nullable: true,
    width: 1,
    default: () => "'0'",
  })
  push_0: boolean | null;

  @Column('tinyint', {
    name: 'push_1',
    nullable: true,
    width: 1,
    default: () => "'0'",
  })
  push_1: boolean | null;

  @Column('tinyint', {
    name: 'push_2',
    nullable: true,
    width: 1,
    default: () => "'0'",
  })
  push_2: boolean | null;

  @Column('tinyint', {
    name: 'push_3',
    nullable: true,
    width: 1,
    default: () => "'0'",
  })
  push_3: boolean | null;

  @Column('int', { name: 'status', nullable: true, default: () => "'0'" })
  status: number | null;

  @Column('int', {
    name: 'total_display_count',
    nullable: true,
    default: () => "'0'",
  })
  total_display_count: number | null;

  @Column('int', {
    name: 'total_redirect_count',
    nullable: true,
    default: () => "'0'",
  })
  total_redirect_count: number | null;

  @Column('datetime', { name: 'created_at', nullable: true })
  created_at: Date | null;

  @OneToMany(() => TblEventLocation, event_location => event_location.event)
  event_location: TblEventLocation[];

  @OneToOne(() => TblSchoolDic, school => school.event)
  @JoinColumn({ name: 'school_id' })
  event_school: TblSchoolDic;
}
