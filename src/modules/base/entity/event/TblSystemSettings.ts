import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';

@Entity('tbl_system_settings', { synchronize: false })
export class TblSystemSettings {
  @PrimaryGeneratedColumn({ type: 'bigint', name: 'id' })
  id: string;

  @Column('varchar', {
    name: 'name',
    nullable: true,
    length: 255,
    default: () => "''",
  })
  name: string | null;

  @Column('varchar', { name: 'value', nullable: true, length: 2000 })
  value: string | null;
}
