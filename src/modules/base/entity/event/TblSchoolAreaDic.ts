import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';

@Entity('tbl_school_area_dic', { synchronize: false })
export class TblSchoolAreaDic {
  @PrimaryGeneratedColumn({ type: 'bigint', name: 'id', unsigned: true })
  id: string;

  @Column('bigint', {
    name: 'university_id',
    nullable: true,
    unsigned: true,
    default: () => "'0'",
  })
  university_id: string | null;

  @Column('varchar', {
    name: 'order',
    nullable: true,
    length: 10,
    default: () => "''",
  })
  order: string | null;

  @Column('varchar', {
    name: 'name',
    nullable: true,
    length: 255,
    default: () => "''",
  })
  name: string | null;

  @Column('varchar', {
    name: 'address',
    nullable: true,
    length: 500,
    default: () => "''",
  })
  address: string | null;

  @Column('varchar', {
    name: 'postcode',
    nullable: true,
    length: 50,
    default: () => "''",
  })
  postcode: string | null;

  @Column('varchar', {
    name: 'area',
    nullable: true,
    length: 255,
    default: () => "''",
  })
  area: string | null;

  @Column('int', {
    name: 'country_id',
    nullable: true,
    unsigned: true,
    default: () => "'0'",
  })
  country_id: number | null;

  @Column('int', {
    name: 'province_id',
    nullable: true,
    unsigned: true,
    default: () => "'0'",
  })
  province_id: number | null;

  @Column('int', {
    name: 'city_id',
    nullable: true,
    unsigned: true,
    default: () => "'0'",
  })
  city_id: number | null;

  @Column('varchar', {
    name: 'timezone',
    nullable: true,
    length: 100,
    default: () => "''",
  })
  timezone: string | null;

  @Column('varchar', {
    name: 'longitude',
    nullable: true,
    length: 50,
    default: () => "''",
  })
  longitude: string | null;

  @Column('varchar', {
    name: 'latitude',
    nullable: true,
    length: 50,
    default: () => "''",
  })
  latitude: string | null;

  @Column('varchar', {
    name: 'phone',
    nullable: true,
    length: 50,
    default: () => "''",
  })
  phone: string | null;

  @Column('varchar', {
    name: 'google_map',
    nullable: true,
    length: 500,
    default: () => "''",
  })
  google_map: string | null;

  @Column('varchar', {
    name: 'baidu_map',
    nullable: true,
    length: 500,
    default: () => "''",
  })
  baidu_map: string | null;

  @Column('varchar', {
    name: 'bing_map',
    nullable: true,
    length: 500,
    default: () => "''",
  })
  bing_map: string | null;

  @Column('datetime', { name: 'created_at' })
  created_at: Date;

  @Column('datetime', { name: 'updated_at' })
  updated_at: Date;

  @Column('datetime', { name: 'deleted_at', nullable: true })
  deleted_at: Date | null;
}
