import {
  Column,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>um<PERSON>,
  <PERSON>To<PERSON>ne,
  PrimaryGeneratedColumn,
} from 'typeorm';
import { TblEventCalendar } from './TblEventCalendar';

@Entity('tbl_event_major', { synchronize: false })
export class TblEventMajor {
  @PrimaryGeneratedColumn({ type: 'bigint', name: 'id', unsigned: true })
  id: string;

  @Column('bigint', { name: 'event_id', nullable: true, unsigned: true })
  event_id: string | null;

  @Column('bigint', { name: 'lid', nullable: true, unsigned: true })
  lid: string | null;

  @Column('bigint', { name: 'major_id', nullable: true, unsigned: true })
  major_id: string | null;

  @Column('varchar', {
    name: 'major_name',
    nullable: true,
    length: 255,
    default: () => "''",
  })
  major_name: string | null;

  @Column('datetime', { name: 'created_at', nullable: true })
  created_at: Date | null;

  @ManyToOne(() => TblEventCalendar, ec => ec.event_major)
  @JoinColumn({ name: 'lid' })
  eventCalendar: TblEventCalendar;
}
