import {
  <PERSON>um<PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>o<PERSON><PERSON>,
  PrimaryGeneratedColumn,
} from 'typeorm';
import { TblGeo } from './TblGeo';

@Entity('tbl_event_iese', { synchronize: false})
export class TblEventIESE {
  @PrimaryGeneratedColumn({ type: 'bigint', name: 'id', unsigned: true })
  id: string;

  @Column('bigint', {
    name: 'event_id',
    nullable: true,
    unsigned: true,
    default: () => "'0'",
  })
  event_id: string | null;

  @Column('int', {
    name: 'event_begin_date',
    nullable: true,
    unsigned: true,
    default: () => "'0'",
  })
  event_begin_date: number | null;

  @Column('int', {
    name: 'event_end_date',
    nullable: true,
    unsigned: true,
    default: () => "'0'",
  })
  event_end_date: number | null;

  @Column('int', {
    name: 'push_begin_date',
    nullable: true,
    unsigned: true,
    default: () => "'0'",
  })
  push_begin_date: number | null;

  @Column('int', {
    name: 'push_end_date',
    nullable: true,
    unsigned: true,
    default: () => "'0'",
  })
  push_end_date: number | null;

  @Column('int', {
    name: 'new_flag_date',
    nullable: true,
    unsigned: true,
    default: () => "'0'",
  })
  new_flag_date: number | null;

  @Column('varchar', {
    name: 'subject',
    nullable: true,
    length: 255,
    default: () => "''",
  })
  subject: string | null;

  @Column('text', { name: 'content', nullable: true })
  content: string | null;

  @Column('varchar', {
    name: 'major',    
    default: () => "''",
  })
  major: string;

  @Column('int', {
    name: 'location_id',
    nullable: true,
    unsigned: true,
    default: () => "'0'",
  })
  location_id: number | null;

  @Column('int', {
    name: 'country_id',
    nullable: true,
    unsigned: true,
    default: () => "'0'",
  })
  country_id: number | null;

  @Column('int', {
    name: 'province_id',
    nullable: true,
    unsigned: true,
    default: () => "'0'",
  })
  province_id: number | null;

  @Column('int', {
    name: 'city_id',
    nullable: true,
    unsigned: true,
    default: () => "'0'",
  })
  city_id: number | null;

  @Column('int', { name: 'status', nullable: true, default: () => "'0'" })
  status: number | null;

  @Column('int', { name: 'created_at', nullable: true })
  created_at: number;

  @OneToOne(() => TblGeo, geo => geo.eventIESE)
  @JoinColumn({ name: 'location_id' })
  event_geo: TblGeo;
}
