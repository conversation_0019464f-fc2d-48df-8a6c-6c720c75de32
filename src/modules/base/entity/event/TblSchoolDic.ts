import {
  Column,
  Entity,
  PrimaryGeneratedColumn,
  OneToOne,
  OneToMany,
} from 'typeorm';
import { TblEvent } from './TblEvent';
import { TblEventCalendar } from './TblEventCalendar';
import { TblSchoolMajorDic } from './TblSchoolMajorDic';

@Entity('tbl_school_dic', { synchronize: false })
export class TblSchoolDic {
  @PrimaryGeneratedColumn({ type: 'bigint', name: 'id', unsigned: true })
  id: string;

  @Column('bigint', {
    name: 'university_id',
    nullable: true,
    unsigned: true,
    default: () => "'0'",
  })
  university_id: string | null;

  @Column('varchar', {
    name: 'display_name',
    nullable: true,
    length: 255,
    default: () => "''",
  })
  display_name: string | null;

  @Column('varchar', {
    name: 'school_name',
    nullable: true,
    length: 255,
    default: () => "''",
  })
  school_name: string | null;

  @Column('varchar', {
    name: 'university_name',
    nullable: true,
    length: 255,
    default: () => "''",
  })
  university_name: string | null;

  @Column('varchar', {
    name: 'keyword',
    nullable: true,
    length: 255,
    default: () => "''",
  })
  keyword: string | null;

  @Column('varchar', {
    name: 'website',
    nullable: true,
    length: 255,
    default: () => "''",
  })
  website: string | null;

  @Column('varchar', {
    name: 'logo_url',
    nullable: true,
    length: 255,
    default: () => "''",
  })
  logo_url: string | null;

  @Column('varchar', {
    name: 'directory',
    nullable: true,
    length: 255,
    default: () => "''",
  })
  directory: string | null;

  @Column('varchar', {
    name: 'country',
    nullable: true,
    length: 255,
    default: () => "''",
  })
  country: string | null;

  @Column('varchar', {
    name: 'short',
    nullable: true,
    length: 255,
    default: () => "''",
  })
  short: string | null;

  @Column('varchar', {
    name: 'area',
    nullable: true,
    length: 255,
    default: () => "''",
  })
  area: string | null;

  @Column('varchar', {
    name: 'school_name_cn',
    nullable: true,
    length: 255,
    default: () => "''",
  })
  school_name_cn: string | null;

  @Column('varchar', {
    name: 'university_name_cn',
    nullable: true,
    length: 255,
    default: () => "''",
  })
  university_name_cn: string | null;

  @Column('varchar', {
    name: 'introduction',
    nullable: true,
    length: 1000,
    default: () => "''",
  })
  introduction: string | null;

  @Column('int', {
    name: 'type',
    nullable: true,
    unsigned: true,
    default: () => "'0'",
  })
  type: number | null;

  @Column('datetime', { name: 'created_at' })
  created_at: Date;

  @Column('datetime', { name: 'updated_at' })
  updated_at: Date;

  @Column('datetime', { name: 'deleted_at', nullable: true })
  deleted_at: Date | null;

  @OneToOne(() => TblEvent, event => event.event_school)
  event: TblEvent;

  @OneToOne(() => TblEventCalendar, event => event.event_school)
  eventCalendar: TblEventCalendar;

  @OneToMany(() => TblSchoolMajorDic, smd => smd.school_dic)
  majors: TblSchoolMajorDic[];
}
