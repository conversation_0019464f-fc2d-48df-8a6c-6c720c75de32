import {
  Column,
  <PERSON>tity,
  PrimaryGeneratedColumn,
  ManyToOne,
  JoinColumn,
  OneToOne,
} from 'typeorm';
import { TblEvent } from './TblEvent';
import { TblEventCalendar } from './TblEventCalendar';

@Entity('tbl_event_location', { synchronize: false })
export class TblEventLocation {
  @PrimaryGeneratedColumn({ type: 'bigint', name: 'id', unsigned: true })
  id: string;

  @Column('bigint', {
    name: 'event_id',
    nullable: true,
    unsigned: true,
    default: () => '0',
  })
  event_id: string | null;

  @Column('bigint', {
    name: 'country_id',
    nullable: true,
    unsigned: true,
    default: () => '0',
  })
  country_id: string | null;

  @Column('bigint', {
    name: 'province_id',
    nullable: true,
    unsigned: true,
    default: () => '0',
  })
  province_id: string | null;

  @Column('bigint', {
    name: 'city_id',
    nullable: true,
    unsigned: true,
    default: () => '0',
  })
  city_id: string | null;

  @Column('int', {
    name: 'begin_date',
    nullable: true,
    unsigned: true,
    default: () => '0',
  })
  begin_date: number | null;

  @Column('int', {
    name: 'end_date',
    nullable: true,
    unsigned: true,
    default: () => '0',
  })
  end_date: number | null;

  @Column('int', {
    name: 'app_join_event',
    nullable: true,
    unsigned: true,
    default: () => '0',
  })
  app_join_event: number | null;

  @Column('int', {
    name: 'multi_day',
    nullable: true,
    unsigned: true,
    default: () => '0',
  })
  multi_day: number | null;

  @Column('datetime', { name: 'created_at', nullable: true })
  created_at: Date | null;

  @ManyToOne(() => TblEvent, event => event.event_location)
  @JoinColumn({ name: 'event_id' })
  event: TblEvent;

  @OneToOne(() => TblEventCalendar, event => event.event_school)
  eventCalendar: TblEventCalendar;
}
