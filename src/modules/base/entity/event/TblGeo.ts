import { Column, Entity, OneToOne, PrimaryGeneratedColumn } from 'typeorm';
import { TblEventCalendar } from './TblEventCalendar';
import { TblEventRelease } from './TblEventRelease';
import { TblEventIESE } from './TblEventIESE';

@Entity('tbl_geo', { synchronize: false })
export class TblGeo {
  @PrimaryGeneratedColumn({ type: 'int', name: 'id', unsigned: true })
  id: number;

  @Column('varchar', { name: 'name', nullable: true, length: 200 })
  name: string | null;

  @Column('int', { name: 'parent_id', nullable: true })
  parent_id: number | null;

  @Column('int', { name: 'order', nullable: true })
  order: number | null;

  @Column('varchar', { name: 'en_name', nullable: true, length: 200 })
  en_name: string | null;

  @Column('varchar', { name: 'keyword', nullable: true, length: 255 })
  keyword: string | null;

  @OneToOne(() => TblEventCalendar, ec => ec.event_geo)
  eventCalendar: TblEventCalendar;

  @OneToOne(() => TblEventRelease, er => er.event_geo)
  eventRelease: TblEventRelease;

  @OneToOne(() => TblEventIESE, er => er.event_geo)
  eventIESE: TblEventIESE;
}
