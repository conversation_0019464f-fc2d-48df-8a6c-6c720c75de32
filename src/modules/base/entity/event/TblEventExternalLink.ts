import { Column, Entity, Index, PrimaryGeneratedColumn } from 'typeorm';

@Entity('tbl_event_external_link', { synchronize: false })
export class TblEventExternalLink {
  @PrimaryGeneratedColumn({ type: 'bigint', name: 'id', unsigned: true })
  id: string;

  @Index()
  @Column('bigint', {
    name: 'event_id',
    nullable: true,
    unsigned: true,
    default: () => "'0'",
  })
  event_id: string | null | number;

  @Column('varchar', {
    name: 'target_url',
    nullable: true,
    length: 1000,
    default: () => "''",
  })
  targetUrl: string | null;

  @Index()
  @Column('varchar', {
    name: 'short_url',
    nullable: true,
    length: 500,
    default: () => "''",
  })
  shortUrl: string | null;

  @Column('int', { name: 'portalNum', default: () => "'0'" })
  portalNum: number | null;

  @Column('int', { name: 'forumNum', default: () => "'0'" })
  forumNum: number | null;

  @Column('int', { name: 'calendarNum', default: () => "'0'" })
  calendarNum: number | null;

  @Column('int', { name: 'ieseNum', default: () => "'0'" })
  ieseNum: number | null;

  @Column('int', { name: 'wechatNum', default: () => "'0'" })
  wechatNum: number | null;

  @Column('int', { name: 'hbNum', default: () => "'0'" })
  hbNum: number | null;

  @Column('int', { name: 'otherNum', default: () => "'0'" })
  otherNum: number | null;

  @Column('int', { name: 'created_at', nullable: true })
  created_at: number | null;
}
