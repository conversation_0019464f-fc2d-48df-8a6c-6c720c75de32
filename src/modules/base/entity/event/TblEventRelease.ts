import {
  <PERSON>um<PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>o<PERSON><PERSON>,
  PrimaryGeneratedColumn,
} from 'typeorm';
import { TblGeo } from './TblGeo';

@Entity('tbl_event_release', { synchronize: false })
export class TblEventRelease {
  @PrimaryGeneratedColumn({ type: 'bigint', name: 'id', unsigned: true })
  id: string;

  @Column('bigint', {
    name: 'event_id',
    nullable: true,
    unsigned: true,
    default: () => "'0'",
  })
  event_id: string | null;

  @Column('int', {
    name: 'event_begin_date',
    nullable: true,
    unsigned: true,
    default: () => "'0'",
  })
  event_begin_date: number | null;

  @Column('int', {
    name: 'event_end_date',
    nullable: true,
    unsigned: true,
    default: () => "'0'",
  })
  event_end_date: number | null;

  @Column('int', {
    name: 'push_begin_date',
    nullable: true,
    unsigned: true,
    default: () => "'0'",
  })
  push_begin_date: number | null;

  @Column('int', {
    name: 'push_end_date',
    nullable: true,
    unsigned: true,
    default: () => "'0'",
  })
  push_end_date: number | null;

  @Column('int', {
    name: 'new_flag_date',
    nullable: true,
    unsigned: true,
    default: () => "'0'",
  })
  new_flag_date: number | null;

  @Column('int', {
    name: 'country_id',
    nullable: true,
    unsigned: true,
    default: () => "'0'",
  })
  country_id: number | null;

  @Column('int', {
    name: 'province_id',
    nullable: true,
    unsigned: true,
    default: () => "'0'",
  })
  province_id: number | null;

  @Column('int', {
    name: 'location_id',
    nullable: true,
    unsigned: true,
    default: () => "'0'",
  })
  location_id: number | null;

  @Column('int', {
    name: 'lid',
    nullable: true,
    unsigned: true,
    default: () => "'0'",
  })
  lid: number | null;

  @Column('text', { name: 'subject', nullable: true })
  subject: string | null;

  @Column('varchar', {
    name: 'url1',
    nullable: true,
    length: 500,
    default: () => "''",
  })
  url1: string | null;

  @Column('varchar', {
    name: 'url2',
    nullable: true,
    length: 500,
    default: () => "''",
  })
  url2: string | null;

  @Column('varchar', {
    name: 'image',
    nullable: true,
    length: 500,
    default: () => "''",
  })
  image: string | null;

  @Column('int', {
    name: 'type',
    nullable: true,
    unsigned: true,
    default: () => "'0'",
  })
  type: number | null;

  @Column('int', { name: 'level', nullable: true, default: () => "'0'" })
  level: number | null;

  @Column('varchar', {
    name: 'color',
    nullable: true,
    length: 50,
    default: () => "''",
  })
  color: string | null;

  @Column('tinyint', {
    name: 'html',
    nullable: true,
    width: 1,
    default: () => "'0'",
  })
  html: boolean | null;

  @Column('int', { name: 'order', nullable: true, default: () => "'0'" })
  order: number | null;

  @Column('int', { name: 'status', nullable: true, default: () => "'0'" })
  status: number | null;

  @Column('datetime', { name: 'created_at', nullable: true })
  created_at: Date | null;

  @OneToOne(() => TblGeo, geo => geo.eventRelease)
  @JoinColumn({ name: 'location_id' })
  event_geo: TblGeo;
}
