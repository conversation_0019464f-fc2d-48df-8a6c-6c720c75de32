import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';

@Entity('tbl_major_category_dic', { synchronize: false })
export class TblMajorCategoryDic {
  @PrimaryGeneratedColumn({ type: 'bigint', name: 'id', unsigned: true })
  id: string;

  @Column('varchar', {
    name: 'type',
    nullable: true,
    length: 50,
    default: () => "''",
  })
  type: string | null;

  @Column('varchar', {
    name: 'field',
    nullable: true,
    length: 200,
    default: () => "''",
  })
  field: string | null;

  @Column('varchar', {
    name: 'display_name',
    nullable: true,
    length: 200,
    default: () => "''",
  })
  display_name: string | null;

  @Column('varchar', {
    name: 'english_name',
    nullable: true,
    length: 200,
    default: () => "''",
  })
  english_name: string | null;

  @Column('varchar', {
    name: 'introduction',
    nullable: true,
    length: 1000,
    default: () => "''",
  })
  introduction: string | null;
}
