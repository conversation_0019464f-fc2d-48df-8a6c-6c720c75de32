import { Column, Entity, Index, PrimaryGeneratedColumn } from 'typeorm';

@Entity('tbl_event_external_link_log', { synchronize: false })
export class TblEventExternalLinkLog {
  @PrimaryGeneratedColumn({ type: 'bigint', name: 'id', unsigned: true })
  id: string;

  @Index()
  @Column('bigint', {
    name: 'external_id',
    nullable: true,
    unsigned: true,
    default: () => "'0'",
  })
  external_id: string | null | number;

  // p: Portal f: Forum c: Calendar
  @Column('varchar', {
    name: 'type',
    nullable: true,
    length: 10,
    default: () => "''",
  })
  type: string | null;

  @Column('varchar', {
    name: 'username',
    nullable: true,
    length: 255,
    default: () => "''",
  })
  username: string | null;

  @Column('varchar', {
    name: 'ip',
    nullable: true,
    length: 50,
    default: () => "''",
  })
  ip: string | null;

  @Column('varchar', {
    name: 'referrer',
    nullable: true,
    length: 255,
    default: () => "''",
  })
  referrer: string | null;

  @Column('varchar', {
    name: 'useragent',
    nullable: true,
    length: 1000,
    default: () => "''",
  })
  useragent: string | null;

  @Column('varchar', {
    name: 'resolution',
    nullable: true,
    length: 50,
    default: () => "''",
  })
  resolution: string | null;

  @Column('int', { name: 'created_at', nullable: true })
  created_at: number | null;
}
