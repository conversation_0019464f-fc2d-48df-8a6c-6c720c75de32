import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';

@Entity('tbl_upload', { synchronize: false })
export class TblUpload {
  @PrimaryGeneratedColumn({ type: 'bigint', name: 'id' })
  id: string;

  @Column('bigint', {
    name: 'school_id',
    nullable: true,
    unsigned: true,
    default: () => "'0'",
  })
  school_id: string | null;

  @Column('varchar', {
    name: 'category',
    nullable: true,
    length: 10,
    default: () => "''",
  })
  category: string | null;

  @Column('int', { name: 'digest', unsigned: true, default: () => "'0'" })
  digest: number;

  @Column('varchar', { name: 'original', length: 100 })
  original: string;

  @Column('varchar', { name: 'fullpath', length: 4000 })
  fullpath: string;

  @Column('varchar', { name: 'type', length: 100 })
  type: string;

  @Column('int', { name: 'order', unsigned: true, default: () => "'0'" })
  order: number;

  @Column('datetime', { name: 'created_at' })
  created_at: Date;
}
