import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';

@Entity('tbl_event_school_major', { synchronize: false })
export class TblEventSchoolMajor {
  @PrimaryGeneratedColumn({ type: 'bigint', name: 'id', unsigned: true })
  id: string;

  @Column('bigint', { name: 'event_id', nullable: true, unsigned: true })
  event_id: string | null;

  @Column('bigint', { name: 'lid', nullable: true, unsigned: true })
  lid: string | null;

  @Column('bigint', { name: 'school_major_id', nullable: true, unsigned: true })
  school_major_id: string | null;

  @Column('datetime', { name: 'created_at', nullable: true })
  created_at: Date | null;
}
