import {
  <PERSON>um<PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  OneToMany,
  OneToOne,
  PrimaryGeneratedColumn,
} from 'typeorm';
import { TblSchoolDic } from './TblSchoolDic';
import { TblEventLocation } from './TblEventLocation';
import { TblGeo } from './TblGeo';
import { TblEventType } from './TblEventType';
import { TblEventMajor } from './TblEventMajor';

@Entity('tbl_event_calendar', { synchronize: false })
export class TblEventCalendar {
  @PrimaryGeneratedColumn({ type: 'bigint', name: 'id', unsigned: true })
  id: string;

  @Column('bigint', {
    name: 'event_id',
    nullable: true,
    unsigned: true,
    default: () => "'0'",
  })
  event_id: string | null;

  @Column('int', {
    name: 'event_begin_date',
    nullable: true,
    unsigned: true,
    default: () => "'0'",
  })
  event_begin_date: number | null;

  @Column('int', {
    name: 'event_end_date',
    nullable: true,
    unsigned: true,
    default: () => "'0'",
  })
  event_end_date: number | null;

  @Column('int', {
    name: 'push_begin_date',
    nullable: true,
    unsigned: true,
    default: () => "'0'",
  })
  push_begin_date: number | null;

  @Column('int', {
    name: 'push_end_date',
    nullable: true,
    unsigned: true,
    default: () => "'0'",
  })
  push_end_date: number | null;

  @Column('int', {
    name: 'new_flag_date',
    nullable: true,
    unsigned: true,
    default: () => "'0'",
  })
  new_flag_date: number | null;

  @Column('int', {
    name: 'school_id',
    nullable: true,
    unsigned: true,
    default: () => "'0'",
  })
  school_id: number | null;

  @Column('int', {
    name: 'location_id',
    nullable: true,
    unsigned: true,
    default: () => "'0'",
  })
  location_id: number | null;

  @Column('int', {
    name: 'lid',
    nullable: true,
    unsigned: true,
    default: () => "'0'",
  })
  lid: number | null;

  @Column('varchar', {
    name: 'subject',
    nullable: true,
    length: 255,
    default: () => "''",
  })
  subject: string | null;

  @Column('varchar', {
    name: 'url1',
    nullable: true,
    length: 500,
    default: () => "''",
  })
  url1: string | null;

  @Column('varchar', {
    name: 'url2',
    nullable: true,
    length: 500,
    default: () => "''",
  })
  url2: string | null;

  @Column('varchar', {
    name: 'url3',
    nullable: true,
    length: 500,
    default: () => "''",
  })
  url3: string | null;

  @Column('varchar', {
    name: 'image',
    nullable: true,
    length: 255,
    default: () => "''",
  })
  image: string | null;

  @Column('int', { name: 'position', nullable: true, default: () => "'0'" })
  position: number | null;

  @Column('int', { name: 'multi_day', nullable: true, default: () => "'0'" })
  multi_day: number | null;

  @Column('int', { name: 'status', nullable: true, default: () => "'0'" })
  status: number | null;

  @Column('bit', {
    transformer: {
      to: value => value,
      from: value => value[0],
    },
  })
  type: number;

  @Column('datetime', { name: 'created_at', nullable: true })
  created_at: Date | null;

  @OneToOne(() => TblSchoolDic, school => school.eventCalendar)
  @JoinColumn({ name: 'school_id' })
  event_school: TblSchoolDic;

  @OneToOne(() => TblEventLocation, el => el.eventCalendar)
  @JoinColumn({ name: 'lid' })
  event_location: TblEventLocation;

  @OneToOne(() => TblGeo, geo => geo.eventCalendar)
  @JoinColumn({ name: 'location_id' })
  event_geo: TblGeo;

  @OneToMany(() => TblEventType, event_type => event_type.eventCalendar)
  @JoinColumn({ name: 'lid' })
  event_type: TblEventType[];

  @OneToMany(() => TblEventMajor, event_major => event_major.eventCalendar)
  @JoinColumn({ name: 'lid' })
  event_major: TblEventMajor[];
}
