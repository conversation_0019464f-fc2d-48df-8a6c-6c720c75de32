import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';

@Entity('tbl_event_apply_calendar', { synchronize: false })
export class TblEventApplyCalendar {
  @PrimaryGeneratedColumn({ type: 'bigint', name: 'id', unsigned: true })
  id: string;

  @Column('bigint', {
    name: 'event_id',
    nullable: true,
    unsigned: true,
    default: () => "'0'",
  })
  event_id: string | null | number;

  @Column('varchar', {
    name: 'subject',
    nullable: true,
    length: 255,
    default: () => "''",
  })
  subject: string | null;

  @Column('text', { name: 'content', nullable: true })
  content: string | null;

  @Column('varchar', {
    name: 'author',
    nullable: true,
    length: 50,
    default: () => "''",
  })
  author: string | null;

  @Column('int', { name: 'status', nullable: true, default: () => "'0'" })
  status: number | null;

  @Column('int', { name: 'created_at', nullable: true })
  created_at: number | null;
}
