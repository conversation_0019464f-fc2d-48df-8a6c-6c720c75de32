import {
  <PERSON>umn,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>um<PERSON>,
  <PERSON><PERSON>o<PERSON><PERSON>,
  PrimaryGeneratedColumn,
} from 'typeorm';
import { TblSchoolDic } from './TblSchoolDic';

@Entity('tbl_school_major_dic', { synchronize: false })
export class TblSchoolMajorDic {
  @PrimaryGeneratedColumn({ type: 'bigint', name: 'id', unsigned: true })
  id: string;

  @Column('bigint', {
    name: 'program_id',
    nullable: true,
    unsigned: true,
    default: () => "'0'",
  })
  program_id: string | null;

  @Column('bigint', {
    name: 'school_id',
    nullable: true,
    unsigned: true,
    default: () => "'0'",
  })
  school_id: string | null;
  //项目学位ID
  @Column('bigint', {
    name: 'program_degrees_id',
    nullable: true,
    unsigned: true,
    default: () => "'0'",
  })
  program_degrees_id: string | null;
  //项目分类ID
  @Column('bigint', {
    name: 'major_id',
    nullable: true,
    unsigned: true,
    default: () => "'0'",
  })
  major_id: string | null;
  //项目类型
  @Column('varchar', {
    name: 'program_type',
    nullable: true,
    length: 20,
    default: () => "''",
  })
  program_type: string | null;
  //项目时长
  @Column('varchar', {
    name: 'program_duration',
    nullable: true,
    length: 20,
    default: () => "''",
  })
  program_duration: string | null;
  //开学时间
  @Column('int', { name: 'school_start_time', nullable: true, unsigned: true })
  school_start_time: number | null;

  @Column('varchar', {
    name: 'fullname_chinese',
    nullable: true,
    length: 255,
    default: () => "''",
  })
  fullname_chinese: string | null;

  @Column('varchar', {
    name: 'fullname_english',
    nullable: true,
    length: 255,
    default: () => "''",
  })
  fullname_english: string | null;

  @Column('varchar', {
    name: 'short',
    nullable: true,
    length: 255,
    default: () => "''",
  })
  short: string | null;

  @Column('datetime', { name: 'created_at', nullable: true })
  created_at: Date | null;

  @ManyToOne(() => TblSchoolDic, sd => sd.majors)
  @JoinColumn({ name: 'school_id' })
  school_dic: TblSchoolDic;
}
