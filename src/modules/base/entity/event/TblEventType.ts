import {
  <PERSON>umn,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>To<PERSON>ne,
  PrimaryGeneratedColumn,
} from 'typeorm';
import { TblEventCalendar } from './TblEventCalendar';

@Entity('tbl_event_type', { synchronize: false })
export class TblEventType {
  @PrimaryGeneratedColumn({ type: 'bigint', name: 'id', unsigned: true })
  id: string;

  @Column('bigint', {
    name: 'event_id',
    nullable: true,
    unsigned: true,
    default: () => "'0'",
  })
  event_id: string | null;

  @Column('bigint', {
    name: 'lid',
    nullable: true,
    unsigned: true,
    default: () => "'0'",
  })
  lid: string | null;

  @Column('bigint', {
    name: 'event_type_id',
    nullable: true,
    unsigned: true,
    default: () => "'0'",
  })
  event_type_id: string | null;

  @Column('varchar', {
    name: 'event_type_name',
    nullable: true,
    length: 255,
    default: () => "''",
  })
  event_type_name: string | null;

  @Column('datetime', { name: 'created_at', nullable: true })
  created_at: Date | null;

  @ManyToOne(() => TblEventCalendar, ec => ec.event_type)
  @JoinColumn({ name: 'lid' })
  eventCalendar: TblEventCalendar;
}
