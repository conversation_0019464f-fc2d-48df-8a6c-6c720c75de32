
import { Column, Entity, OneToMany } from 'typeorm';

import { BaseEntity } from '@cool-midway/core';
import { BlogTagEntity } from './blogTag';

/**
 * Blog
 */
@Entity('iese_blog', { synchronize: false })
export class BlogEntity extends BaseEntity {
  @Column({ comment: 'UUID', unique: true, default: '' })
  uuid: string;

  @Column({ comment: '展示图', default: '' })
  pic: string;

  @Column({ comment: '标题', default: '' })
  title: string;

  @Column({ comment: '内容', type:'text', default: '' })
  content: string;

  @Column({ comment: '作者', default: '' })
  author: string;

  @Column({ comment: '日期', default: 0 })
  datetime: number;

  @Column({ comment: '状态', default: 0 })
  status: number;

  @OneToMany(() => BlogTagEntity, type => type.blog, {
    createForeignKeyConstraints: false,
  })
  tags: BlogTagEntity[];
}
