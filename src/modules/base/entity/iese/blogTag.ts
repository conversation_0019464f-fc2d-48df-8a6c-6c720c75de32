
import { Column, Entity, Index, ManyToOne } from 'typeorm';

import { BaseEntity } from '@cool-midway/core';
import { BlogEntity } from './blog';

/**
 * Blog Tag
 */
@Entity('iese_blog_tag', { synchronize: false })
export class BlogTagEntity extends BaseEntity {
  @Column({ comment: '标签ID', default: 0 })
  tagId: number;

  @ManyToOne(() => BlogEntity, blog => blog.tags, {
    createForeignKeyConstraints: false,
  })
  blog: BlogEntity;
}
