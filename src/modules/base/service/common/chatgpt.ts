import { Utils } from './../../../../comm/utils';
import { Inject, Provide } from '@midwayjs/decorator';
import { BaseService } from '@cool-midway/core';
import { InjectEntityModel } from '@midwayjs/typeorm';
import { Config } from '@midwayjs/decorator';
import { Context } from '@midwayjs/koa';
import { Repository, Like } from 'typeorm';
import * as _ from 'lodash';
import { ChatgptSessionEntity } from '../../entity/chatgpt/chatgptSession';
import { ChatgptSessionDetailEntity } from '../../entity/chatgpt/chatgptSessionDetail';
import { ChatgptTagEntity } from '../../entity/chatgpt/chatgptTag';
import { ChatgptUserTagEntity } from '../../entity/chatgpt/chatgptUserTag';
import { BaseSysUserEntity } from '../../entity/sys/user';
import { v1 as uuid } from 'uuid';
import { ChatgptSessionUploadEntity } from '../../entity/chatgpt/chatgptSessionUpload';
import * as fse from 'fs-extra';
import axios from 'axios';
import OpenAI from 'openai';
import { ChatgptGPTsAuthEntity } from '../../entity/chatgpt/chatgptGPTsAuth';
import { ChatOpenAI } from '@langchain/openai';
import { ChatAnthropic } from '@langchain/anthropic';
import { ChatPromptTemplate } from '@langchain/core/prompts';
import { AiVenderEntity } from '../../entity/chatgpt/aiVender';
import { AiVenderModelEntity } from '../../entity/chatgpt/aiVenderModel';
import { HumanMessage } from '@langchain/core/messages';
import { MessagesPlaceholder } from '@langchain/core/prompts';
const FormData = require('form-data');
const fs = require('fs');

/**
 * ChatGPT
 */
@Provide()
export class ChatgptService extends BaseService {
  @Inject()
  ctx: Context;

  @Inject()
  baseDir;

  @Inject()
  utils: Utils;

  @Config('chatgpt')
  chatgpt;

  @Config('moonshot')
  moonshot;

  @Config('anthropic')
  anthropic;

  @InjectEntityModel(ChatgptTagEntity)
  chatgptTagEntity: Repository<ChatgptTagEntity>;

  @InjectEntityModel(ChatgptUserTagEntity)
  chatgptUserTagEntity: Repository<ChatgptUserTagEntity>;

  @InjectEntityModel(ChatgptSessionEntity)
  chatgptSessionEntity: Repository<ChatgptSessionEntity>;

  @InjectEntityModel(ChatgptSessionDetailEntity)
  chatgptSessionDetailEntity: Repository<ChatgptSessionDetailEntity>;

  @InjectEntityModel(ChatgptSessionUploadEntity)
  chatgptSessionUploadEntity: Repository<ChatgptSessionUploadEntity>;

  @InjectEntityModel(BaseSysUserEntity)
  baseSysUserEntity: Repository<BaseSysUserEntity>;

  @InjectEntityModel(ChatgptGPTsAuthEntity)
  chatgptGPTsAuthEntity: Repository<ChatgptGPTsAuthEntity>;

  @InjectEntityModel(AiVenderEntity)
  aiVenderEntity: Repository<AiVenderEntity>;

  @InjectEntityModel(AiVenderModelEntity)
  aiVenderModelEntity: Repository<AiVenderModelEntity>;

  async gptsAuthPage(param: any) {
    const take = param.take || 15;
    const skip = param.skip || 1;

    return await this.chatgptGPTsAuthEntity.findAndCount({
      where: {},
      take,
      skip: (skip - 1) * take,
    });
  }

  async gptsAuthAdd(param: any) {
    param.password = this.utils.encodeDZString(param.password);

    await this.chatgptGPTsAuthEntity.save(param);
  }

  async gptsAuthUpdate(param: any) {
    if (param?.password?.length) {
      param.password = this.utils.encodeDZString(param.password);
    } else {
      delete param.password;
    }

    await this.chatgptGPTsAuthEntity.update(param.id, param);
  }

  async gptsAuthDelete(param: any) {
    await this.chatgptGPTsAuthEntity.delete(param.id);
  }

  async gptsAuth(param) {
    let authHeader = this.ctx.req.headers?.authorization;
    const token = authHeader?.split(' ')[1];
    if (token !== 'UMjXMG4oXmfaA8CotaWL') return { msg: '非法请求' };

    if (!param?.username?.trim()) {
      return { msg: '请提供有效的用户名' };
    }

    const user = await this.chatgptGPTsAuthEntity.findOne({
      where: {
        username: param.username,
      },
    });

    if (!user) {
      return { msg: '验证失败' };
    }

    if (param?.username?.trim() && param?.password?.trim()) {
      const password = this.utils.decodeDZString(user.password);

      if (user && password === param.password.trim())
        return { msg: '验证成功' };
      else return { msg: '验证失败' };
    } else {
      return { msg: '请提供密码' };
    }
  }

  async voiceToText(param, aiVM) {
    try {
      const regex = /<source src="([^"]+)"\s*type="audio\/mpeg">/;
      const match = param.content.match(regex);

      if (!match) {
        this.ctx.emit('conversationErr', 'No URL found');
        return;
      }

      const url = match[1];

      const prefix =
        this.ctx.app.getEnv() === 'prod'
          ? `https://connect.chasedream.com/upload/chatgpt/`
          : `http://localhost:9000/dev/admin/base/open/previewGptMp3/`;

      let path = url.split(prefix)[1];
      if (this.ctx.app.getEnv() === 'prod') {
        path = `${this.baseDir}/../upload/chatgpt/${path}`;
      } else {
        path = `${this.baseDir}/../upload/chatgpt/${path.replaceAll('-', '/')}`;
      }

      const data = new FormData();
      data.append('file', fs.createReadStream(path));
      data.append('model', 'whisper-1');
      data.append('response_format', 'text');

      const response = await axios.post(
        `${this.chatgpt.apiBaseUrl}/audio/transcriptions`,
        data,
        {
          headers: {
            Authorization: `Bearer ${this.chatgpt.apiKey}`,
            ...data.getHeaders(),
          },
          timeout: 60000,
        }
      );

      const res: any = {};
      const messageId = uuid();
      const isNew = param?.parentMessageId?.length ? false : true;
      res.isNew = isNew;

      res.sessionModelType = aiVM.type;
      res.id = uuid();
      res.text = response.data;

      if (isNew) {
        const cs = await this.chatgptSessionEntity.save({
          uid: this.ctx.admin.userId,
          subject:
            param?.subject?.length > 0
              ? param.subject?.trim()
              : `新会话-${this.utils.today('YYYYMMDD-HH:mm:ss')}`,
          lastMessageId: messageId,
          lasMsgText: res.text?.substring(0, 255),
          model: param?.model,
          lastMsgTime: this.utils.now(),
        });

        res.sessionId = cs.id;
        res.subject = cs.subject;

        await this.chatgptSessionDetailEntity.save({
          uid: this.ctx.admin.userId,
          sessionId: cs.id,
          type: 0,
          content: param.content,
        });
        await this.chatgptSessionDetailEntity.save({
          uid: this.ctx.admin.userId,
          sessionId: cs.id,
          messageId,
          type: 1,
          content: res.text,
        });
      } else {
        await this.chatgptSessionEntity.update(
          {
            id: param.sessionId,
          },
          {
            lastMessageId: messageId,
            lasMsgText: res.text?.substring(0, 255),
            lastMsgTime: this.utils.now(),
          }
        );
        await this.chatgptSessionDetailEntity.save({
          uid: this.ctx.admin.userId,
          sessionId: param.sessionId,
          type: 0,
          content: param.content,
        });
        await this.chatgptSessionDetailEntity.save({
          uid: this.ctx.admin.userId,
          sessionId: param.sessionId,
          messageId: messageId,
          type: 1,
          content: res.text,
        });
      }

      this.ctx.emit('conversationRes', res);
    } catch (err) {
      this.ctx.emit('conversationErr', err.message);
    }
  }

  async textToVoice(param, aiVM) {
    const openai = new OpenAI({
      apiKey: this.chatgpt.apiKey,
      baseURL: this.chatgpt.apiBaseUrl,
      timeout: this.chatgpt.timeoutMs,
      maxRetries: 5,
    });

    const yearMonthDate = this.utils.yearMonthDate();
    const filePath = `${this.baseDir}/../upload/chatgpt/${yearMonthDate}`;
    const fileName = `${this.utils.today('YYYYMMDDHHmmss')}`;
    const ext = '.mp3';
    const speechFile = `${filePath}${fileName}${ext}`;

    try {
      const mp3 = await openai.audio.speech.create({
        model: 'tts-1',
        voice: param.voiceVal || 'alloy',
        input: param.content,
      });

      let url =
        this.ctx.app.getEnv() === 'prod'
          ? `https://connect.chasedream.com/upload/chatgpt/${yearMonthDate}${fileName}${ext}`
          : `http://localhost:9000/dev/admin/base/open/previewGptMp3/${yearMonthDate.replaceAll(
              '/',
              '-'
            )}${fileName}${ext}`;

      url = `<audio controls preload="none"><source src="${url}" type="audio/mpeg"></audio>`;
      const buffer = Buffer.from(await mp3.arrayBuffer());

      await fse.ensureDir(filePath);
      await fse.writeFile(speechFile, buffer);

      const res: any = {};
      const messageId = uuid();
      const isNew = param?.parentMessageId?.length ? false : true;
      res.isNew = isNew;

      res.sessionModelType = aiVM.type;
      res.id = uuid();
      res.url = url;

      if (isNew) {
        const cs = await this.chatgptSessionEntity.save({
          uid: this.ctx.admin.userId,
          subject:
            param?.subject?.length > 0
              ? param.subject?.trim()
              : `新会话-${this.utils.today('YYYYMMDD-HH:mm:ss')}`,
          lastMessageId: messageId,
          lasMsgText: param.content?.substring(0, 255),
          model: param?.model,
          voiceVal: param?.voiceVal,
          lastMsgTime: this.utils.now(),
        });

        res.sessionId = cs.id;
        res.subject = cs.subject;

        await this.chatgptSessionDetailEntity.save({
          uid: this.ctx.admin.userId,
          sessionId: cs.id,
          type: 0,
          content: param.content,
        });
        await this.chatgptSessionDetailEntity.save({
          uid: this.ctx.admin.userId,
          sessionId: cs.id,
          messageId,
          type: 1,
          content: url,
        });
      } else {
        await this.chatgptSessionEntity.update(
          {
            id: param.sessionId,
          },
          {
            voiceVal: param?.voiceVal,
            lastMessageId: messageId,
            lasMsgText: param.content?.substring(0, 255),
            lastMsgTime: this.utils.now(),
          }
        );
        await this.chatgptSessionDetailEntity.save({
          uid: this.ctx.admin.userId,
          sessionId: param.sessionId,
          type: 0,
          content: param.content,
        });
        await this.chatgptSessionDetailEntity.save({
          uid: this.ctx.admin.userId,
          sessionId: param.sessionId,
          messageId: messageId,
          type: 1,
          content: url,
        });
      }

      this.ctx.emit('conversationRes', res);
    } catch (err) {
      this.ctx.emit('conversationErr', err.message);
    }
  }

  async generateImageFromText(param, aiVM) {
    const openai = new OpenAI({
      apiKey: this.chatgpt.apiKey,
      baseURL: this.chatgpt.apiBaseUrl,
      timeout: this.chatgpt.timeoutMs,
      maxRetries: 5,
    });

    const res: any = {};
    const messageId = uuid();
    const isNew = param?.parentMessageId?.length ? false : true;
    res.isNew = isNew;

    const yearMonthDate = this.utils.yearMonthDate();
    const filePath = `${this.baseDir}/../upload/chatgpt/${yearMonthDate}`;
    const fileName = `${this.utils.today('YYYYMMDDHHmmss')}`;
    const ext = '.png';

    let imgUrl =
      this.ctx.app.getEnv() === 'prod'
        ? `https://connect.chasedream.com/upload/chatgpt/${yearMonthDate}${fileName}${ext}`
        : `http://localhost:9000/dev/admin/base/open/previewGptImg/${yearMonthDate.replaceAll(
            '/',
            '-'
          )}${fileName}${ext}`;
    imgUrl = `<a href="${imgUrl}" target="_blank" style="cursor: pointer"><img src="${imgUrl}" width="200" height="200" /></a>`;

    res.sessionModelType = aiVM.type;
    res.imgUrl = imgUrl;
    res.id = uuid();

    if (isNew) {
      const cs = await this.chatgptSessionEntity.save({
        uid: this.ctx.admin.userId,
        subject:
          param?.subject?.length > 0
            ? param.subject?.trim()
            : `新会话-${this.utils.today('YYYYMMDD-HH:mm:ss')}`,
        lastMessageId: messageId,
        lasMsgText: param.content?.substring(0, 255),
        model: param?.model,
        lastMsgTime: this.utils.now(),
      });

      try {
        const response: any = await openai.images.generate({
          prompt: param.content,
          n: 1,
          size: '1024x1024',
          response_format: 'b64_json',
        });

        const bs64img = response.data[0]?.b64_json;

        await this.utils.base64ToImage(bs64img, `${filePath}${fileName}${ext}`);

        res.sessionId = cs.id;
        res.subject = cs.subject;

        await this.chatgptSessionDetailEntity.save({
          uid: this.ctx.admin.userId,
          sessionId: cs.id,
          type: 0,
          content: param.content,
        });
        await this.chatgptSessionDetailEntity.save({
          uid: this.ctx.admin.userId,
          sessionId: cs.id,
          messageId,
          type: 1,
          content: imgUrl,
        });

        this.ctx.emit('conversationRes', res);
      } catch (err) {
        this.ctx.emit('conversationErr', err.message);
        throw new Error(err.message);
      }
    } else {
      try {
        const response: any = await openai.images.generate({
          prompt: param.content,
          n: 1,
          size: '1024x1024',
          response_format: 'b64_json',
        });

        const bs64img = response.data[0]?.b64_json;

        await this.utils.base64ToImage(bs64img, `${filePath}${fileName}${ext}`);

        await this.chatgptSessionEntity.update(
          {
            id: param.sessionId,
          },
          {
            lastMessageId: messageId,
            lasMsgText: param.content?.substring(0, 255),
            lastMsgTime: this.utils.now(),
          }
        );
        await this.chatgptSessionDetailEntity.save({
          uid: this.ctx.admin.userId,
          sessionId: param.sessionId,
          type: 0,
          content: param.content,
        });
        await this.chatgptSessionDetailEntity.save({
          uid: this.ctx.admin.userId,
          sessionId: param.sessionId,
          messageId: messageId,
          type: 1,
          content: imgUrl,
        });

        this.ctx.emit('conversationRes', res);
      } catch (err) {
        this.ctx.emit('conversationErr', err.message);
      }
    }
  }

  async sessionTags(sessionid: number) {
    const where = {
      uid: this.ctx.admin.userId,
      sessionid,
    };

    return await this.chatgptUserTagEntity.find({
      where,
      order: {
        id: 'DESC',
      },
    });
  }

  async bindTag(param) {
    const exist = await this.chatgptUserTagEntity.findOne({
      where: {
        uid: this.ctx.admin.userId,
        sessionid: param.sessionid,
        tagid: param.tagid,
      },
    });

    if (exist) return;

    return await this.chatgptUserTagEntity.save({
      uid: this.ctx.admin.userId,
      sessionid: param.sessionid,
      tagid: param.tagid,
    });
  }

  async unbindTag(param) {
    await this.chatgptUserTagEntity.delete({
      uid: this.ctx.admin.userId,
      sessionid: param.sessionid,
      tagid: param.tagid,
    });
  }

  async tags(sessionid: number) {
    const where = {
      uid: this.ctx.admin.userId,
    };

    const res = await this.chatgptTagEntity.find({
      where,
      order: {
        id: 'DESC',
      },
    });

    const uts = await this.chatgptUserTagEntity.find({
      where: {
        sessionid: sessionid,
        uid: this.ctx.admin.userId,
      },
    });

    const tagids = uts.map(el => el.tagid);
    res.map(el => {
      // @ts-ignore
      tagids.includes(el.id) ? (el.effect = 'dark') : (el.effect = 'plain');
    });

    return res;
  }

  async addTag(param) {
    const res = await this.chatgptTagEntity.save({
      uid: this.ctx.admin.userId,
      tag: param.tag,
    });

    await this.chatgptUserTagEntity.save({
      uid: this.ctx.admin.userId,
      sessionid: param.sessionid,
      tagid: res.id,
    });

    return res;
  }

  async delTag(param) {
    await this.chatgptTagEntity.delete({
      id: param.id,
      uid: this.ctx.admin.userId,
    });

    await this.chatgptUserTagEntity.delete({
      tagid: param.id,
      uid: this.ctx.admin.userId,
    });
  }

  async sessions(param) {
    const take = param.take || 10;
    const skip = param.skip || 1;
    const userId = param.userId || 0;

    const find = await this.chatgptSessionEntity.createQueryBuilder('session');

    if (param.subject) {
      let tagids: any = await this.chatgptTagEntity.find({
        where: {
          tag: Like(`%${param.subject}%`),
        },
      });
      tagids = [...tagids].map(row => row.id);

      find.where('session.subject like :subject', {
        subject: `%${param.subject}%`,
      });

      if (tagids.length) {
        find.orWhere('tag.tagid in (:...tagids)', {
          tagids,
        });
      }
    }

    if (param.module !== 'all' || !this.utils.isAdmin(this.ctx)) {
      find.andWhere('session.status >= 0');
    }

    const res = await find
      .leftJoinAndSelect(
        ChatgptUserTagEntity,
        'tag',
        'tag.sessionid = session.id'
      )
      .andWhere('session.uid = :uid', {
        uid: userId === 0 ? this.ctx.admin.userId : param.userId,
      })
      .orderBy('session.digest', 'DESC')
      .addOrderBy('session.lastMsgTime', 'DESC')
      .take(take)
      .skip((skip - 1) * take)
      .getManyAndCount();

    for (let row of res[0]) {
      let tags = await this.chatgptUserTagEntity.find({
        where: {
          uid: userId === 0 ? this.ctx.admin.userId : param.userId,
          sessionid: row.id,
        },
      });

      const tagids = tags.map(el => el.tagid);

      // @ts-ignore
      row.tags = await this.chatgptTagEntity.findByIds(tagids);
    }

    return res;
  }

  async aiVender(param) {
    return await this.aiVenderEntity.find({
      where: {
        status: 0,
      },
      order: {
        order: 'DESC',
      },
    });
  }

  async aiVenderModel(param) {
    return await this.aiVenderModelEntity.find({
      where: {
        venderId: param.venderId,
      },
      order: {
        order: 'DESC',
      },
    });
  }

  async details(param) {
    const userId = param.userId || 0;
    const userQueryId = userId === 0 ? this.ctx.admin.userId : param.userId;

    const details = await this.chatgptSessionDetailEntity
      .createQueryBuilder('detail')
      .leftJoinAndSelect('detail.uploads', 'upload')
      .where('detail.uid = :uid', { uid: userQueryId })
      .andWhere('detail.sessionId = :sessionId', { sessionId: param.sessionId })
      .orderBy('detail.id', 'ASC')
      .getMany();

    const session = await this.chatgptSessionEntity.findOneBy({
      id: param.sessionId,
    });
    const aiVM = await this.aiVenderModelEntity.findOneBy({
      id: session.model,
    });

    return { details, aiVM };
  }

  async updateSubject(param) {
    return await this.chatgptSessionEntity.update(
      {
        uid: this.ctx.admin.userId,
        id: param.id,
      },
      {
        subject: param.subject,
        digest: param.digest ? 1 : 0,
      }
    );
  }

  async deleteSession(param) {
    await this.chatgptSessionEntity
      .createQueryBuilder()
      .update()
      .where({
        id: param.id,
        uid: this.ctx.admin.userId,
      })
      .set({ status: -1 })
      .execute();
  }

  async destroySession(param) {
    await this.chatgptSessionEntity.delete({
      id: param.id,
    });

    await this.chatgptSessionDetailEntity.delete({
      sessionId: param.id,
    });
  }

  async conversation(param) {
    const aiVM = await this.aiVenderModelEntity.findOneBy({ id: param.model });
    if (!aiVM) throw new Error('未找到模型');

    switch (aiVM.type) {
      case 'chat':
      case 'multimodal':
        this.conversation01(param, aiVM);
        break;
      case 'image':
        this.generateImageFromText(param, aiVM);
        break;
      case 'tts':
        this.textToVoice(param, aiVM);
        break;
      case 'whisper':
        this.voiceToText(param, aiVM);
        break;
      default:
        throw new Error('非法参数');
    }
  }

  async conversation01(param, aiVM) {
    let model = aiVM.model;

    const finishReasons = [
      'length',
      'stop',
      'content_filter',
      'tool_calls',
      'function_call',
      'max_tokens',
    ];

    const vender = await this.aiVenderEntity.findOneBy({ id: aiVM.venderId });

    if (param?.parentMessageId?.length) {
      const messages = [];
      let sds = await this.chatgptSessionDetailEntity.find({
        where: {
          sessionId: param.sessionId,
        },
        order: {
          id: 'DESC',
        },
        take: this.chatgpt.context,
        relations: ['uploads'],
      });

      sds = sds.sort((a, b) => a.id - b.id);

      for (const sd of sds) {
        const msg = await this.utils.genMessages4GPT(
          sd.content,
          sd.uploads,
          sd.type === 0 ? 'human' : 'ai'
        );
        messages.push(msg);
      }

      const chat = await this.getVender(vender.name, model);

      const prompt = ChatPromptTemplate.fromMessages([
        new MessagesPlaceholder('messages'),
        new HumanMessage(param.content),
      ]);

      // const prompt = ChatPromptTemplate.fromMessages([
      //   ...context,
      //   [new HumanMessage(param.content)],
      // ]);

      // @ts-ignore
      const chain = prompt.pipe(chat);
      const stream = await chain.stream({
        messages,
      });

      try {
        let text = '';
        const messageId = uuid();

        let res: any = {};

        // @ts-ignore
        for await (const chunk of stream) {
          res = Object.assign({}, chunk);
          res.id = messageId;

          text += chunk?.content || '';

          this.ctx.emit('conversationRes', res);
        }

        await this.chatgptSessionEntity.update(
          {
            id: param.sessionId,
          },
          {
            lastMessageId: messageId,
            lasMsgText: text?.substring(0, 255),
            lastMsgTime: this.utils.now(),
          }
        );
        const sd = await this.chatgptSessionDetailEntity.save({
          uid: this.ctx.admin.userId,
          sessionId: param.sessionId,
          type: 0,
          content: param.content,
        });
        await this.chatgptSessionDetailEntity.save({
          uid: this.ctx.admin.userId,
          sessionId: param.sessionId,
          messageId: res?.id,
          type: 1,
          content: text,
        });

        for (const url of param.uploadFiles) {
          await this.chatgptSessionUploadEntity.update(url.id, {
            detailId: sd.id,
          });
        }

        res.text = text;
        this.ctx.emit('conversationRes', res);
      } catch (err) {
        this.ctx.emit('conversationErr', err.message);
      }
    } else {
      const cs = await this.chatgptSessionEntity.save({
        uid: this.ctx.admin.userId,
        subject:
          param?.subject?.length > 0
            ? param.subject?.trim()
            : `新会话-${this.utils.today('YYYYMMDD-HH:mm:ss')}`,
        lastMessageId: '',
        lasMsgText: '',
        model: param?.model,
        lastMsgTime: this.utils.now(),
      });

      try {
        const msg = await this.utils.genMessages4GPT(
          param.content,
          param.uploadFiles,
          'human'
        );

        const chat = await this.getVender(vender.name, model);
        const prompt = ChatPromptTemplate.fromMessages([msg]);
        // @ts-ignore
        const chain = prompt.pipe(chat);
        const stream = await chain.stream({});

        let text = '';
        const messageId = uuid();
        let res: any = {};

        // @ts-ignore
        for await (let chunk of stream) {
          res = Object.assign({}, chunk);
          res.id = messageId;

          text += chunk?.content || '';

          this.ctx.emit('conversationRes', res);
        }

        res.sessionId = cs.id;
        res.subject = cs.subject;
        res.isNew = true;

        await this.chatgptSessionEntity.update(
          {
            id: cs.id,
          },
          {
            lastMessageId: messageId,
            lasMsgText: text?.substring(0, 255),
            lastMsgTime: this.utils.now(),
          }
        );
        const sd = await this.chatgptSessionDetailEntity.save({
          uid: this.ctx.admin.userId,
          sessionId: cs.id,
          type: 0,
          content: param.content,
        });
        await this.chatgptSessionDetailEntity.save({
          uid: this.ctx.admin.userId,
          sessionId: cs.id,
          messageId: messageId,
          type: 1,
          content: text,
        });

        for (const url of param.uploadFiles) {
          await this.chatgptSessionUploadEntity.update(url.id, {
            detailId: sd.id,
          });
        }

        res.text = text;
        this.ctx.emit('conversationRes', res);
      } catch (err) {
        this.ctx.emit('conversationErr', err.message);
        throw new Error(err.message);
      }
    }
  }

  async getVender(vender: string, model: string) {
    switch (vender) {
      case 'OpenAI':
        {
          return new ChatOpenAI({
            apiKey: this.chatgpt.apiKey,
            modelName: model,
            timeout: this.chatgpt.timeoutMs,
            temperature: this.chatgpt.completionParams.temperature,
            configuration: {
              baseURL: this.chatgpt.apiBaseUrl,
              maxRetries: 5,
            },
          });
        }
        break;

      case 'MoonShot':
        {
          return new ChatOpenAI({
            apiKey: this.moonshot.apiKey,
            modelName: model,
            timeout: this.moonshot.timeoutMs,
            temperature: this.moonshot.completionParams.temperature,
            configuration: {
              baseURL: this.moonshot.apiBaseUrl,
              maxRetries: 5,
            },
          });
        }
        break;

      case 'Anthropic':
        return new ChatAnthropic({
          apiKey: this.anthropic.apiKey,
          modelName: model,
          temperature: this.anthropic.completionParams.temperature,
          clientOptions: {
            baseURL: this.anthropic.apiBaseUrl,
            maxRetries: 5,
            timeout: this.anthropic.timeoutMs,
          },
        });
        break;

      default:
        break;
    }
  }

  async users() {
    const arr = await this.chatgptSessionEntity
      .createQueryBuilder('')
      .select('uid')
      .distinct(true)
      .getRawMany();

    const uids = [...arr].map(el => el.uid);

    return await this.baseSysUserEntity
      .createQueryBuilder()
      .select(['id', 'username'])
      .where('id in (:...uids)', { uids })
      .getRawMany();
  }
}
