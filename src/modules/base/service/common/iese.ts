import { Inject, Provide } from '@midwayjs/decorator';
import { BaseService } from '@cool-midway/core';
import { InjectEntityModel } from '@midwayjs/typeorm';
import { Repository, ILike } from 'typeorm';
import { Utils } from '../../../../comm/utils';
import { BlogEntity } from '../../entity/iese/blog';
import { BlogTagEntity } from '../../entity/iese/blogTag';
import { BlogTagsEntity } from '../../entity/iese/blogTags';
import { v1 as uuid } from 'uuid';

import * as _ from 'lodash';

/**
 * IESE Service
 */
@Provide()
export class IESEService extends BaseService {
  @InjectEntityModel(BlogEntity)
  blogEntity: Repository<BlogEntity>;

  @InjectEntityModel(BlogTagEntity)
  blogTagEntity: Repository<BlogTagEntity>;

  @InjectEntityModel(BlogTagsEntity)
  blogTagsEntity: Repository<BlogTagsEntity>;
  

  @Inject()
  utils: Utils;

  async findOne(param: any) {
    return await this.blogEntity.findOne({
      relations: ['tags'],
      where: { id: param.id },
    })
  }

  async pages(param: any) {
    const { id, title } = param;

    return await this.blogEntity.findAndCount({
      relations: ['tags'],
      where: {
        ...(id !== undefined && { id }),        
        ...(title && {
          title: ILike(`%${title}%`)
        }),
      },
      order: {
        datetime: 'DESC',
        id: 'DESC'
      },
      skip: param.pageSize * (param.page - 1),
      take: param.pageSize,
    });
  }

  async create(param: any) {
    param.uuid = uuid().replaceAll('-', '');

    const res = await this.blogEntity.save(param);

    for(const tagId of param.tags) {
      await this.blogTagEntity.save({
        tagId,
        blog: res,
      });
    }
  }

  async edit(param: any) {
    await this.blogTagEntity.delete({blog: param.id});
    const res = await this.blogEntity.findOneBy({id:param.id});

    for(const tagId of param.tags) {
      await this.blogTagEntity.save({
        tagId,
        blog: res,
      });
    }

    delete param.tags;

    await this.blogEntity.update(param.id, {
      ...param,
    });
  }

  async del(param: any) {
    await this.blogEntity.delete({ id: param.id });
    await this.blogTagEntity.delete({ blog: { id: param.id } });
  }

  async tags(param: any) {
    return await this.blogTagsEntity.find({
      order: {
        order: 'DESC',
      },
    });
  }
}
