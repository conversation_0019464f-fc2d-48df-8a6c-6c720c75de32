import {
  Inject,
  Logger,
  Config,
  Init,
  Autoload,
  App,
} from '@midwayjs/decorator';
import { InjectEntityModel } from '@midwayjs/typeorm';
import { Application as SocketApplication } from '@midwayjs/socketio';
import { Repository } from 'typeorm';
import { ILogger } from '@midwayjs/logger';
import { Context } from '@midwayjs/koa';
import * as _ from 'lodash';
import * as fse from 'fs-extra';
import { promises as fs } from 'fs';
import { extname } from 'path';
import { WechatMessageEntity } from '../../entity/wx/wechatMessage';
import { WechatyBuilder, ScanStatus, Wechaty } from 'wechaty';
import { generate, setErrorLevel } from 'qrcode-terminal';
import { WechatGroupEntity } from '../../entity/wx/wechatGroup';
import { WechatService } from './wechat';
import { Utils } from '../../../../comm/utils';
import { BaseSysConfService } from '../sys/conf';
const sizeOf = require('image-size');

/**
 * Wechat Bot
 */
@Autoload()
export class WechatBotService {
  @Inject()
  ctx: Context;

  @App('socketIO')
  socketApp: SocketApplication;

  @InjectEntityModel(WechatMessageEntity)
  wechatMessageEntity: Repository<WechatMessageEntity>;

  @InjectEntityModel(WechatGroupEntity)
  wechatGroupEntity: Repository<WechatGroupEntity>;

  @Inject()
  baseSysConfService: BaseSysConfService;

  @Inject()
  wechatService: WechatService;

  @Inject()
  baseDir;

  @Inject()
  utils: Utils;

  @Logger()
  logger: ILogger;

  @Config('wechaty')
  wechaty;

  wechatBot: Wechaty;

  @Init()
  async init() {
    if (!this.wechaty.enable) return;

    try {
      this.wechatBot = WechatyBuilder.build(this.wechaty);
      global.wechatBot = this.wechatBot;

      this.wechatBot
        .on('scan', this.onScan.bind(this))
        .on('login', this.onLogin.bind(this))
        .on('logout', this.onLogout.bind(this))
        .on('message', this.onMessage.bind(this));

      await this.wechatBot.start();
      console.log('Wechaty bot started.');
    } catch (e) {
      console.error('Wechaty bot start error:', e);
    }
  }

  onScan(qrcode: string, status: ScanStatus) {
    setErrorLevel('L');
    generate(qrcode, { small: true });

    const qrcodeImageUrl = [
      'https://api.qrserver.com/v1/create-qr-code/?data=',
      encodeURIComponent(qrcode),
    ].join('');

    this.baseSysConfService.updateVaule('wechatQrCode', qrcodeImageUrl);
    console.log(`[${status}] ${qrcodeImageUrl}\n扫描上面的二维码: `);
  }

  async onLogout(user: any) {
    global.wechatInfo = {};
    console.log(`微信用户 ${user} 退出登录`);
  }

  async onLogin(user: any) {
    global.wechatInfo = {
      id: user.id,
      name: user.payload.name,
      avatar: user.payload.avatar,
    };
    console.log(`微信用户 ${user} 已登录`);
  }

  async onMessage(msg: any) {
    const talker = msg.talker();
    const text = msg.text();
    const room = msg.room();
    const type = msg.type();
    const wxNickname = talker.name();
    const wxId = talker.id;

    const topic = (await room?.topic()) || '';
    const roomId = room?.id || '';

    let avatar = (await msg.talker().avatar()).toJSON();
    avatar = avatar.url;

    let newMessage = undefined;

    try {
      let group = undefined;

      try {
        if (topic.length) {
          group = await this.wechatGroupEntity.findOne({
            where: {
              roomId,
            },
          });

          if (!group) {
            const attachFileBox = await room!.avatar();
            const attachData = await attachFileBox.toBuffer();
            const ext = '.jpg';

            const yearMonthDate = this.utils.yearMonthDate();
            const filePath = `${this.baseDir}/../upload/wechat/message/${yearMonthDate}`;
            const filename = `${this.utils.today('YYYYMMDDHHmmss')}`;

            await fse.ensureDir(filePath);
            await fs.writeFile(`${filePath}/${filename}${ext}`, attachData);

            let avatar = (await room.avatar()).toJSON();
            avatar = avatar.url;

            group = await this.wechatGroupEntity.save({
              roomId,
              topic,
              memberCount: room.payload.memberIdList.length,
              avatar,
            });
          }
        } else {
          group = await this.wechatGroupEntity.findOne({
            where: {
              wxId,
            },
          });

          if (!group) {
            group = await this.wechatGroupEntity.save({
              roomId: wxId,
              wxId,
              wxNickname,
              avatar,
            });
          }
        }
      } catch (error) {
        this.logger.error(error.message);
      }

      switch (type) {
        case this.wechatBot.Message.Type.Text: {
          newMessage = await this.wechatMessageEntity.save({
            roomId,
            topic,
            wxId,
            wxNickname,
            text,
            type,
            groupId: group.id,
            avatar,
          });
          break;
        }

        case this.wechatBot.Message.Type.Emoticon: {
          const emotionFile = await msg.toFileBox();
          const emotionData = await emotionFile.toBuffer();
          const ext = extname(emotionFile.name);

          const yearMonthDate = this.utils.yearMonthDate();
          const filePath = `${this.baseDir}/../upload/wechat/message/${yearMonthDate}`;
          const filename = `${this.utils.today('YYYYMMDDHHmmss')}`;

          await fse.ensureDir(filePath);
          await fs.writeFile(`${filePath}/${filename}${ext}`, emotionData);

          await this.wechatMessageEntity.save({
            roomId,
            topic,
            wxId,
            wxNickname,
            text,
            type,
            filename: `${emotionFile.name.substring(
              0,
              emotionFile.name.lastIndexOf('.')
            )}`,
            filesize: emotionFile.metadata.payload.len,
            attachment:
              filePath.replace(this.baseDir + '/..', '') + filename + ext,
            width: emotionFile.metadata.payload.width,
            height: emotionFile.metadata.payload.height,
            groupId: group.id,
            avatar,
          });

          break;
        }

        case this.wechatBot.Message.Type.Url: {
          const urlLink = await msg.toUrlLink();

          await this.wechatMessageEntity.save({
            roomId,
            topic,
            wxId,
            wxNickname,
            text: urlLink.payload.url,
            type,
            groupId: group.id,
            avatar,
          });

          break;
        }

        case this.wechatBot.Message.Type.Image: {
          const messageImage = await msg.toImage();
          const artworkImage = await messageImage.artwork();
          const artworkImageData = await artworkImage.toBuffer();
          const ext = extname(artworkImage.name);

          const yearMonthDate = this.utils.yearMonthDate();
          const filePath = `${this.baseDir}/../upload/wechat/message/${yearMonthDate}`;
          const filename = `${this.utils.today('YYYYMMDDHHmmss')}`;

          await fse.ensureDir(filePath);
          await fs.writeFile(`${filePath}/${filename}${ext}`, artworkImageData);
          const dimensions = sizeOf(`${filePath}/${filename}${ext}`);

          await this.wechatMessageEntity.save({
            roomId,
            topic,
            wxId,
            wxNickname,
            text,
            type,
            filename: `${artworkImage.name.substring(
              0,
              artworkImage.name.lastIndexOf('.')
            )}`,
            filesize: artworkImage.size,
            attachment:
              filePath.replace(this.baseDir + '/..', '') + filename + ext,
            width: dimensions.width,
            height: dimensions.height,
            groupId: group.id,
            avatar,
          });

          break;
        }

        case this.wechatBot.Message.Type.Attachment: {
          const attachFileBox = await msg.toFileBox();
          const attachData = await attachFileBox.toBuffer();
          const ext = extname(attachFileBox.name);

          const yearMonthDate = this.utils.yearMonthDate();
          const filePath = `${this.baseDir}/../upload/wechat/message/${yearMonthDate}`;
          const filename = `${this.utils.today('YYYYMMDDHHmmss')}`;

          await fse.ensureDir(filePath);
          await fs.writeFile(`${filePath}/${filename}${ext}`, attachData);

          const stat = await fs.stat(`${filePath}/${filename}${ext}`);

          await this.wechatMessageEntity.save({
            roomId,
            topic,
            wxId,
            wxNickname,
            text,
            type,
            filename: `${attachFileBox.name.substring(
              0,
              attachFileBox.name.lastIndexOf('.')
            )}`,
            filesize: stat.size,
            attachment:
              filePath.replace(this.baseDir + '/..', '') + filename + ext,
            groupId: group.id,
            avatar,
          });

          break;
        }

        case this.wechatBot.Message.Type.Audio: {
          const audioFileBox = await msg.toFileBox();
          const audioData = await audioFileBox.toBuffer();
          const ext = extname(audioFileBox.name);
          const mp3 = '.mp3';

          const yearMonthDate = this.utils.yearMonthDate();
          const filePath = `${this.baseDir}/../upload/wechat/message/${yearMonthDate}`;
          const filename = `${this.utils.today('YYYYMMDDHHmmss')}`;

          await fse.ensureDir(filePath);
          await fs.writeFile(`${filePath}/${filename}${ext}`, audioData);

          await this.utils.runShellCommand(
            `slk2mp3 ${filePath}/${filename}${ext} ${filePath}/${filename}${mp3} && rm -rf ${filePath}/${filename}${ext}`
          );

          const stat = await fs.stat(`${filePath}/${filename}${mp3}`);

          await this.wechatMessageEntity.save({
            roomId,
            topic,
            wxId,
            wxNickname,
            text,
            type,
            filename: audioFileBox.name.replace(ext, ''),
            filesize: stat.size,
            attachment:
              filePath.replace(this.baseDir + '/..', '') + filename + mp3,
            groupId: group.id,
            avatar,
          });

          break;
        }

        case this.wechatBot.Message.Type.Video: {
          const videoFileBox = await msg.toFileBox();
          const videoData = await videoFileBox.toBuffer();
          const ext = extname(videoFileBox.name);

          const yearMonthDate = this.utils.yearMonthDate();
          const filePath = `${this.baseDir}/../upload/wechat/message/${yearMonthDate}`;
          const filename = `${this.utils.today('YYYYMMDDHHmmss')}`;

          await fse.ensureDir(filePath);
          await fs.writeFile(`${filePath}/${filename}${ext}`, videoData);

          const stat = await fs.stat(`${filePath}/${filename}${ext}`);

          await this.wechatMessageEntity.save({
            roomId,
            topic,
            wxId,
            wxNickname,
            text,
            type,
            filename: videoFileBox.name.replace(ext, ''),
            filesize: stat.size,
            attachment:
              filePath.replace(this.baseDir + '/..', '') + filename + ext,
            groupId: group.id,
            avatar,
          });

          break;
        }

        case this.wechatBot.Message.Type.MiniProgram: {
          // const miniProgram = await msg.toMiniProgram();

          break;
        }

        default:
          break;
      }

      await this.wechatGroupEntity.update(group.id, {
        topic,
        memberCount: topic.length ? room.payload.memberIdList.length : 0,
        lasMsgText: text,
        lastMsgTime: this.utils.now(),
      });

      this.socketApp.of('/wx/wechat').emit('newWechatMessage', newMessage);
    } catch (err) {
      this.logger.error(err.message);
    }
  }
}
