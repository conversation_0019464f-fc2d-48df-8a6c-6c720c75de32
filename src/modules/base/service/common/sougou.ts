import { Inject, Provide } from '@midwayjs/decorator';
import { BaseService } from '@cool-midway/core';
import { InjectEntityModel } from '@midwayjs/typeorm';
import { Repository } from 'typeorm';
import { CommonSougouEntity } from '../../entity/common/sougou';
import { Utils } from '../../../../comm/utils';
import { readFileSync, readdirSync } from 'node:fs';
import { CoolElasticSearch } from '@cool-midway/es';
import * as _ from 'lodash';
import * as xlsx from 'node-xlsx';
const path = require('node:path');

/**
 * 搜狗
 */
@Provide()
export class CommonSougouService extends BaseService {
  @InjectEntityModel(CommonSougouEntity)
  commonSougouEntity: Repository<CommonSougouEntity>;

  @Inject()
  es: CoolElasticSearch;

  @Inject()
  utils: Utils;

  @Inject()
  baseDir;

  async create(param) {
    const res = await this.commonSougouEntity.save({
      subject: param.subject,
      content: param.content,
      type: param.type,
    });

    return res;
  }

  async search(param) {
    const index = 'sougou';
    param.from = param.from || 1;
    param.size = param.size || 10;

    return await this.es.client.search({
      index,
      size: param.size,
      from: (param.from - 1) * param.size,
      query: {
        bool: {
          must: {
            match: {
              content: {
                query: param.s,
                operator: 'and',
              },
            },
          },
          filter: {
            bool: {
              must: [{ term: { type: param.type } }],
            },
          },
        },
      },
      highlight: {
        fields: {
          content: {
            pre_tags: ['<b class="highlight">'],
            post_tags: ['</b>'],
          },
        },
      },
    });
  }

  async more(param) {
    const index = 'sougou';

    return await this.es.client.search({
      index,
      query: {
        ids: {
          values: param.ids,
        },
      },
    });
  }

  async update(param: any) {
    const index = 'sougou';
    const id = param.id;

    const model = {
      subject: param.subject,
      content: param.content,
    };

    if (_.isEmpty(param.subject)) {
      delete model.subject;
    }

    await this.commonSougouEntity
      .createQueryBuilder()
      .update()
      .where({ id })
      .set(model)
      .execute();

    await this.es.client.update({
      index,
      id,
      doc: model,
    });

    await this.es.client.indices.refresh({ index });
  }

  async initCr() {
    const type = 'cr';
    const index = 'sougou';
    const basePath = `${this.baseDir}/${type}`;
    const files = readdirSync(basePath)?.filter(el => el.endsWith('.md'));

    for (const file of files) {
      const ext = path.extname(`${basePath}/${file}`);
      let filename = path.basename(file, ext);
      let md = readFileSync(`${basePath}/${file}`).toString('utf8');

      while (true) {
        const len = [...md.matchAll(/(\n\n\s)|(\n\n\n)/gm)];
        if (len.length === 0) break;
        md = md.replace(/(\n\n\s)|(\n\n\n)/gm, '\n\n');
      }

      const reg = /\n((\d{1,3}(\.|\\\.|、)?)|(#.*?<a.*?\/a>)|题目\d{1,3})+/gm;
      let contents = md
        .split(reg)
        .map(e => {
          return e
            ?.replace(/(^\n\n)/, '')
            ?.replace(/\n\n$/, '')
            ?.replace(/(\d{1,3}(\.|\\\.)?)|(#.*?<a.*?\/a>)|(^\.)/g, '')
            ?.replace(/^\\\./, '')
            ?.replace(/^题目|^、/, '')
            ?.trim();
        })
        .filter(el => !_.isEmpty(el));
      contents = contents.filter(el => !_.isEmpty(el?.trim()));

      let arr = [];
      for (let i = 1; i <= contents.length; i++) {
        const content = contents[i - 1]?.trim();

        arr.push({
          subject: '',
          content,
          type,
          filename,
        });

        if (i % 1000 === 0 || contents.length === i) {
          await this.commonSougouEntity
            .createQueryBuilder()
            .insert()
            .values(arr)
            .execute();

          const operations = arr.flatMap(doc => {
            doc.createTime = this.utils.now();
            delete doc.updateTime;

            return [{ index: { _index: index, _id: doc.id } }, doc];
          });

          await this.es.client.bulk({ operations });

          arr = [];
        }
      }
    }

    await this.es.client.indices.refresh({ index });
  }

  async initQuant() {
    const type = 'quant';
    const index = 'sougou';
    const basePath = `${this.baseDir}/${type}`;
    const mds = readdirSync(basePath)?.filter(el => el.endsWith('.md'));

    for (const file of mds) {
      const ext = path.extname(`${basePath}/${file}`);
      let filename = path.basename(file, ext);
      let md = readFileSync(`${basePath}/${file}`).toString('utf8');

      while (true) {
        const len = [...md.matchAll(/(\n\n\s)|(\n\n\n)/gm)];
        if (len.length === 0) break;
        md = md.replace(/(\n\n\s)|(\n\n\n)/gm, '\n\n');
      }

      const reg =
        /(^(题号)?\d{1,3}$)|(^\d{1,3} .*)|(^(__)?\d{1,3}(\.|\\\.(__)?|、))/gm;
      let contents = md
        .split(reg)
        .map(e => {
          return e
            ?.replace(/\n{1,3}(.*)?\]\(data:image/, '![](data:image')
            ?.replace(/(^\n\n)/, '')
            ?.replace(/\n\n$/, '')
            ?.replace(
              /(^(题号)?\d{1,3}$)|(^\d{1,3} .*)|(^(__)?\d{1,3}(\.|\\\.(__)?|、))/gm,
              ''
            )
            ?.replace(/^__$/g, '')
            ?.replace(/^\\.(__)?$/g, '')
            ?.replace(/^(\.|、|题号)$/g, '')
            ?.replace(/!\[[^\n\]]+([^\]]*)\]\(data:image/, '![](data:image')
            ?.trim();
        })
        .filter(el => !_.isEmpty(el));
      contents = contents.filter(el => !_.isEmpty(el?.trim()));

      let arr = [];
      for (let i = 1; i <= contents.length; i++) {
        const content = contents[i - 1]?.trim();

        arr.push({
          subject: '',
          content,
          type,
          filename,
        });

        if (i % 10 === 0 || contents.length === i) {
          await this.commonSougouEntity
            .createQueryBuilder()
            .insert()
            .values(arr)
            .execute();

          const operations = arr.flatMap(doc => {
            doc.createTime = this.utils.now();
            delete doc.updateTime;

            return [{ index: { _index: index, _id: doc.id } }, doc];
          });

          await this.es.client.bulk({ operations });

          arr = [];
        }
      }
    }

    await this.es.client.indices.refresh({ index });
  }

  async initIr() {
    const type = 'ir';
    const index = 'sougou';
    const basePath = `${this.baseDir}/${type}`;
    const mds = readdirSync(basePath)?.filter(el => el.endsWith('.md'));

    for (const file of mds) {
      const ext = path.extname(`${basePath}/${file}`);
      let filename = path.basename(file, ext);
      let md = readFileSync(`${basePath}/${file}`).toString('utf8');

      while (true) {
        const len = [...md.matchAll(/(\n\n\s)|(\n\n\n)/gm)];
        if (len.length === 0) break;
        md = md.replace(/(\n\n\s)|(\n\n\n)/gm, '\n\n');
      }

      const reg =
        /(^(题号)?\d{1,3}$)|(^\d{1,3} .*)|(^(__)?\d{1,3}(\.|\\\.(__)?|、))/gm;
      let contents = md
        .split(reg)
        .map(e => {
          return e
            ?.replace(/\n{1,3}(.*)?\]\(data:image/, '![](data:image')
            ?.replace(/(^\n\n)/, '')
            ?.replace(/\n\n$/, '')
            ?.replace(
              /(^(题号)?\d{1,3}$)|(^\d{1,3} .*)|(^(__)?\d{1,3}(\.|\\\.(__)?|、))/gm,
              ''
            )
            ?.replace(/^__$/g, '')
            ?.replace(/^\\.(__)?$/g, '')
            ?.replace(/^(\.|、|题号)$/g, '')
            ?.replace(/!\[[^\n\]]+([^\]]*)\]\(data:image/, '![](data:image')
            ?.trim();
        })
        .filter(el => !_.isEmpty(el));
      contents = contents.filter(el => !_.isEmpty(el?.trim()));

      let arr = [];
      for (let i = 1; i <= contents.length; i++) {
        const content = contents[i - 1]?.trim();

        arr.push({
          subject: '',
          content,
          type,
          filename,
        });

        if (i % 10 === 0 || contents.length === i) {
          await this.commonSougouEntity
            .createQueryBuilder()
            .insert()
            .values(arr)
            .execute();

          const operations = arr.flatMap(doc => {
            doc.createTime = this.utils.now();
            delete doc.updateTime;

            return [{ index: { _index: index, _id: doc.id } }, doc];
          });

          await this.es.client.bulk({ operations });

          arr = [];
        }
      }
    }

    await this.es.client.indices.refresh({ index });
  }

  async initRc(type) {
    const basePath = `${this.baseDir}/${type}`;
    const files = readdirSync(basePath);

    for (const file of files) {
      const ext = path.extname(`${basePath}/${file}`);
      let filename = path.basename(file, ext);

      if (['.xls', '.xlsx'].includes(ext)) {
        //excel文档
        const sheets = xlsx.parse(`${basePath}/${file}`);

        let subjects = sheets[0].data.map((el: any[]) => {
          if (el.length > 1) {
            return el[1];
          }
        });

        let contents = [];
        for (let i = 0; i < subjects.length; i++) {
          const sheet = sheets[i + 1];

          const content = sheet.data
            .filter((el: any[]) => el.length === 1)
            .map(el => el[0]?.trim())
            .join('\n');
          contents.push(content);
        }

        contents = contents.filter(el => !_.isEmpty(el.trim()));

        await this.rcHandler(filename, subjects, contents, type);
      } else if (['.md'].includes(ext)) {
        //markdown 文档
        let content = readFileSync(`${basePath}/${file}`).toString('utf8');

        const reg1 = /\s+#(.*)/gi;
        const reg2 = /.*<a id=.*<\/a>(.*)/gi;
        const reg = filename.endsWith('S1') ? reg2 : reg1;

        filename = filename.endsWith('S1')
          ? filename.replace('-S1', '')
          : filename;

        let subjects = [];
        let contents = [];
        const els = content.split(reg);

        for (let i = 0; i < els.length; i++) {
          if (i % 2 === 0) {
            contents.push(els[i]);
          } else {
            subjects.push(els[i]);
          }
        }

        await this.rcHandler(filename, subjects, contents, type);
      }
    }
  }

  async rcHandler(filename, subjects, contents, type) {
    const index = 'sougou';

    let arr = [];
    for (let i = 1; i <= contents.length; i++) {
      const subject = subjects[i - 1]
        ?.replace(/<a(.*)<\/a>/g, '')
        ?.replace(/\d+\\./g, '')
        ?.trim();
      const content = contents[i - 1]
        ?.replace(/(^\n\n)/, '')
        ?.replace(/\n\n$/, '')
        ?.trim();

      if (!_.isEmpty(content)) {
        arr.push({
          subject: subject || '',
          content,
          type,
          filename,
        });
      }

      if (i % 1000 === 0 || contents.length === i) {
        await this.commonSougouEntity
          .createQueryBuilder()
          .insert()
          .values(arr)
          .execute();

        const operations = arr.flatMap(doc => {
          doc.createTime = this.utils.now();
          delete doc.updateTime;

          return [{ index: { _index: index, _id: doc.id } }, doc];
        });

        await this.es.client.bulk({ operations });

        arr = [];
      }
    }

    await this.es.client.indices.refresh({ index });
  }

  async initIndex() {
    const index = 'sougou';

    try {
      await this.es.client.indices.delete({
        index,
      });
    } catch (err) {
      console.log(err.message);
    }

    await this.es.client.indices.create({
      index,
      mappings: {
        properties: {
          subject: {
            type: 'text',
            analyzer: 'ik_max_word',
          },
          content: {
            type: 'text',
            analyzer: 'ik_max_word',
          },
          type: {
            type: 'keyword',
          },
          filename: {
            type: 'keyword',
          },
          createTime: {
            type: 'integer',
          },
        },
      },
    });
  }

  async initData(param: any) {
    switch (param.type) {
      case 'rc':
        await this.initRc('rc');
        break;

      case 'rc2':
        await this.initRc('rc2');
        break;

      case 'cr':
        await this.initCr();
        break;

      case 'quant':
        await this.initQuant();
        break;

      case 'ir':
        await this.initIr();
        break;
    }
  }
}
