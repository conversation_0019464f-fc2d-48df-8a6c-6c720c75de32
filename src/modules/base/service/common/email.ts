import { Inject, Provide } from '@midwayjs/decorator';
import { BaseService } from '@cool-midway/core';
import { InjectEntityModel } from '@midwayjs/typeorm';
import { Repository } from 'typeorm';
import { Context } from '@midwayjs/koa';
import * as _ from 'lodash';
import { MailEntity } from '../../entity/mail/mail';
import { Utils } from '../../../../comm/utils';
const nodemailer = require('nodemailer');

/**
 * Email
 */
@Provide()
export class EmailService extends BaseService {
  @Inject()
  ctx: Context;

  @Inject()
  utils: Utils;

  @InjectEntityModel(MailEntity)
  mailEntity: Repository<MailEntity>;

  async sendmail(param: any) {
    const model: any = {
      from: '<EMAIL>',
      to: param.toemail,
      subject: param.subject,
      message: param.message,
      status: '发送成功！',
    };

    const transporter = nodemailer.createTransport({
      host: 'smtp.office365.com',
      port: 587,
      secure: false,
      auth: {
        user: '<EMAIL>',
        pass: 'qEV8,ke7)JBY',
      },
      tls: {
        ciphers: 'SSLv3',
      },
    });

    let ip = await this.utils.getReqIP(this.ctx);
    ip = _.isArray(ip) ? ip[0] : ip;
    model.ip = ip;

    transporter.sendMail({
      from: model.from,
      to: model.to,
      subject: model.subject,
      html: model.message,
    });

    await this.mailEntity.save(model);
  }
}
