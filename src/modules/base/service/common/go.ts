import { Inject, Provide, Logger, Task, FORMAT } from '@midwayjs/decorator';
import { BaseService } from '@cool-midway/core';
import { CoolCommException } from '@cool-midway/core';
import { InjectEntityModel } from '@midwayjs/typeorm';
import { MoreThanOrEqual, Not } from 'typeorm';
import { Repository } from 'typeorm';
import { Utils } from '../../../../comm/utils';
import { ILogger } from '@midwayjs/logger';
import * as _ from 'lodash';
import { Url } from '../../entity/go/Url';
import { UrlDetails } from '../../entity/go/UrlDetails';
import { Context } from '@midwayjs/koa';
import { CcCommonMember } from '../../entity/forum/CcCommonMember';
import * as useragent from 'useragent';
import { UrlStatistics } from '../../entity/go/UrlStatistics';
import xlsx from 'node-xlsx';

/**
 * Go
 */
@Provide()
export class GoService extends BaseService {
  @Inject()
  ctx: Context;

  @InjectEntityModel(Url, 'go')
  url: Repository<Url>;

  @InjectEntityModel(UrlDetails, 'go')
  urlDetails: Repository<UrlDetails>;

  @InjectEntityModel(UrlStatistics, 'go')
  urlStatistics: Repository<UrlStatistics>;

  @InjectEntityModel(CcCommonMember, 'forum')
  ccCommonMember: Repository<CcCommonMember>;

  @Logger()
  logger: ILogger;

  @Inject()
  utils: Utils;

  @Inject()
  baseDir;

  async update(param: any) {
    console.log(param);
  }

  async export(param: any) {
    if (param.form == 1 || param.form == 2) {
      return await this.exportDetail(param);
    } else if (param.form == 3) {
      return await this.exportStatistic(param);
    }
  }

  async exportDetail(param: any) {
    let date = '';
    const queryBuilder = await this.urlDetails.createQueryBuilder('urlDetails');
    queryBuilder.select('*');
    queryBuilder
      .where('urlDetails.OriginalUrl = :OriginalUrl', {
        OriginalUrl: param.OriginalUrl,
      })
      .andWhere('urlDetails.IsDeleted = 0');

    if (param.daterange instanceof Array) {
      const start = this.utils.timestampToUtc(param.daterange[0], true);
      const end = this.utils.timestampToUtc(param.daterange[1], true);

      queryBuilder.andWhere(
        `urlDetails.Date >= ${start} and urlDetails.Date <= ${end}`
      );

      date = `-${this.utils.timeStampToDate(
        start,
        'YYYYMMDD'
      )}-${this.utils.timeStampToDate(end, 'YYYYMMDD')}`;
    } else if (
      param.detailFrom &&
      param.detailFilter >= 0 &&
      param.detailFilter <= 3
    ) {
      const typeVal = param.detailFrom;
      let type = '';

      switch (param.detailFilter) {
        case 1:
          type = `urlDetails.Year = ${typeVal}`;
          break;
        case 2:
          type = `urlDetails.Month = ${typeVal}`;
          break;
        case 3:
          type = `urlDetails.Week = ${typeVal}`;
          break;
      }

      queryBuilder.andWhere(type);
    }

    const details = await queryBuilder.getRawMany();

    const data = [];

    const title = [
      'ID',
      'ForumName',
      'OriginalUrl',
      'TargetUrl',
      'Referrer',
      'IPAddress',
      'BrowserType',
      'Platform',
      'VisitTime',
    ];

    data.push(title);

    for (const detail of details) {
      data.push([
        detail.ID,
        detail.ForumName,
        detail.OriginalUrl,
        detail.TargetUrl,
        detail.Referrer,
        detail.IPAddress,
        detail.BrowserType,
        detail.Platform,
        this.utils.timeStampToDate(detail.VisitTime),
      ]);
    }

    // @ts-ignore
    const buffer = xlsx.build([{ data: data }]);

    const fileName = `GoDetail-${param.OriginalUrl.replaceAll(
      '/',
      '-'
    )}${date}.xlsx`;

    return {
      file: buffer,
      fileName,
    };
  }

  async exportStatistic(param: any) {
    let date = '';
    const queryBuilder = await this.urlStatistics.createQueryBuilder(
      'urlStatistics'
    );
    queryBuilder.select('*');
    queryBuilder
      .where('urlStatistics.OriginalUrl = :OriginalUrl', {
        OriginalUrl: param.OriginalUrl,
      })
      .andWhere('urlStatistics.IsDeleted = 0');

    if (param.daterange instanceof Array) {
      const start = this.utils.timestampToUtc(param.daterange[0], true);
      const end = this.utils.timestampToUtc(param.daterange[1], true);

      queryBuilder.andWhere(
        `urlStatistics.CreateTime >= ${start} and urlStatistics.CreateTime <= ${end}`
      );

      date = `-${this.utils.timeStampToDate(
        start,
        'YYYYMMDD'
      )}-${this.utils.timeStampToDate(end, 'YYYYMMDD')}`;
    }

    const items = await queryBuilder.getRawMany();

    const data = [];

    const title = [
      'ID',
      'OriginalUrl',
      'Hits',
      'Year',
      'Month',
      'Week',
      'CreateTime',
    ];

    data.push(title);

    for (const item of items) {
      data.push([
        item.ID,
        item.OriginalUrl,
        item.Hits,
        item.Year,
        item.Month,
        item.Week,
        this.utils.timeStampToDate(item.CreateTime, 'YYYY-MM-DD'),
      ]);
    }

    // @ts-ignore
    const buffer = xlsx.build([{ data: data }]);

    const fileName = `GoStatistic-${param.OriginalUrl.replaceAll(
      '/',
      '-'
    )}${date}.xlsx`;

    return {
      file: buffer,
      fileName,
    };
  }

  async urlStatisticsDel(param: any) {
    await this.urlStatistics.update(param.ID, {
      IsDeleted: true,
    });
  }

  async statisticByUrl(param: any) {
    const page = param.page || 1;
    const size = param.size || 50;
    const OriginalUrl: string = param.OriginalUrl;
    let total = 0;
    let sum = {};

    const queryBuilder = this.urlStatistics.createQueryBuilder('urlStat');
    const countQueryBuilder = this.urlStatistics.createQueryBuilder('urlStat');
    const sumQueryBuilder = this.urlStatistics.createQueryBuilder('urlStat');

    queryBuilder.select('*');
    countQueryBuilder.select('COUNT(*) count');
    sumQueryBuilder.select('SUM(Hits) sum');

    if (param.filter > 0 && param.filter <= 3) {
      let type = '';
      switch (param.filter) {
        case 1:
          queryBuilder.groupBy('urlStat.Year');
          type = 'urlStat.Year';
          break;
        case 2:
          queryBuilder.groupBy('urlStat.Month');
          type = 'urlStat.Month';
          break;
        case 3:
          queryBuilder.groupBy('urlStat.Week');
          type = 'urlStat.Week';
          break;
      }

      const result = await this.nativeQuery(
        `SELECT COUNT(1) cnt from (
              SELECT
                  ${type} 
                FROM
                  UrlStatistics urlStat 
                WHERE
                  urlStat.OriginalUrl = '${OriginalUrl}' 
                  AND urlStat.IsDeleted = 0 
                GROUP BY
                ${type}) sub`,
        [],
        'go'
      );

      total = result[0].cnt;

      type += ' as CreateTime';

      queryBuilder
        .select([type, 'SUM(urlStat.Hits) as Hits'])
        .orderBy('CreateTime', 'DESC');
    } else {
      queryBuilder.orderBy('urlStat.ID', 'DESC');

      countQueryBuilder
        .where('urlStat.OriginalUrl = :OriginalUrl', { OriginalUrl })
        .andWhere('urlStat.IsDeleted = 0');

      sumQueryBuilder
        .where('urlStat.OriginalUrl = :OriginalUrl', { OriginalUrl })
        .andWhere('urlStat.IsDeleted = 0');

      if (param.filter === 4 && param.daterange instanceof Array) {
        const start = this.utils.timestampToUtc(param.daterange[0], true);
        const end = this.utils.timestampToUtc(param.daterange[1], true);

        countQueryBuilder.andWhere(
          `urlStat.CreateTime >= ${start} and urlStat.CreateTime <= ${end}`
        );

        sumQueryBuilder.andWhere(
          `urlStat.CreateTime >= ${start} and urlStat.CreateTime <= ${end}`
        );
      }

      total = await countQueryBuilder.getCount();
      sum = await sumQueryBuilder.getRawOne();
    }

    queryBuilder
      .where('urlStat.OriginalUrl = :OriginalUrl', { OriginalUrl })
      .andWhere('urlStat.IsDeleted = 0')
      .take(size)
      .skip((page - 1) * size);

    if (param.filter === 4 && param.daterange instanceof Array) {
      const start = this.utils.timestampToUtc(param.daterange[0], true);
      const end = this.utils.timestampToUtc(param.daterange[1], true);

      queryBuilder.andWhere(
        `urlStat.CreateTime >= ${start} and urlStat.CreateTime <= ${end}`
      );
    }

    const list = await queryBuilder.getRawMany();

    if(page === 1) {
      const today = this.utils.timestampToUtc(this.utils.todayTS());
      const count = await this.urlDetails.count({
        where: {
          OriginalUrl,
          Date: MoreThanOrEqual(today.toString())
        }
      })

      if(count > 0 && list.length > 0) {
        list.unshift({
          Hits: count,
          CreateTime: this.utils.timestampToUtc(this.utils.todayTS(), true)
        })
      }
    }

    return {
      list,
      total,
      // @ts-ignore
      sum: sum?.sum,
    };
  }

  async detailByUrl(param: any) {
    const page = param.page || 1;
    const size = param.size || 50;
    const OriginalUrl: string = param.OriginalUrl;
    let type = '';
    let typeVal = param.CreateTime;

    let query = this.urlDetails
      .createQueryBuilder('urlDetail')
      .where('urlDetail.OriginalUrl = :OriginalUrl', { OriginalUrl })
      .andWhere('urlDetail.IsDeleted = 0');

    if (param.filter >= 0 && param.filter <= 4) {
      switch (param.filter) {
        case 1:
          type = `urlDetail.Year = ${typeVal}`;
          break;
        case 2:
          type = `urlDetail.Month = ${typeVal}`;
          break;
        case 3:
          type = `urlDetail.Week = ${typeVal}`;
          break;
        case 4:
          type = `urlDetail.Date = ${typeVal}`;
          break;
        default:
          type = `urlDetail.Date = ${typeVal}`;
          break;
      }

      query = query.andWhere(type);
    }

    const list = await query
      .orderBy('urlDetail.ID', 'DESC')
      .skip((page - 1) * size)
      .take(size)
      .getMany();

    const total = await query.getCount();

    return {
      list,
      total,
    };
  }

  async queryGroup(param: any) {
    const queryBuilder = this.urlDetails.createQueryBuilder('urlDetail');

    queryBuilder
      .select([
        'urlDetail.Query as query',
        'COUNT(*) as count'
      ])
      .where('urlDetail.OriginalUrl = :originalUrl', {
        originalUrl: param.OriginalUrl
      })
      .andWhere('urlDetail.IsDeleted = 0');
    
    if (param.filter === 4 && param.daterange instanceof Array) {
      const start = this.utils.timestampToUtc(param.daterange[0], true);
      const end = this.utils.timestampToUtc(param.daterange[1], true);

      queryBuilder.andWhere(
        `urlDetail.VisitTime >= ${start} and urlDetail.VisitTime <= ${end}`
      );
    }
    
    queryBuilder
      .groupBy('urlDetail.Query')
      .orderBy('count', 'DESC');

    const results = await queryBuilder.getRawMany();
    
    return results.map(item => ({
      name: item.query || '空',
      count: parseInt(item.count)
    }));
  }

  async urlPage() {
    const where = {
      IsDeleted: false,
    };

    const urls = await this.url.find({
      where,
      order: {
        leaf: 'ASC',
        OriginalUrl: 'ASC',
      },
    });

    return this.utils.deepTree(urls);
  }

  async urlByID(param: any) {
    return await this.url.findOneBy({ ID: param.ID });
  }

  async urlSub(param: any) {
    const data = await this.url.findOneBy({ ID: param.ID });

    const where = {
      IsDeleted: false,
      FatherPath: `${data.FatherPath}${param.ID}|`,
    };

    return await this.url.find({
      where,
      order: {
        OriginalUrl: 'ASC',
      },
    });
  }

  async urlAdd(param: any) {
    param.CreateTime = this.utils.today();
    param.IsDeleted = false;
    param.leaf = true;
    param.iconCls = 'page';
    param.Enable = true;
    param.FatherID = param.FatherID || 0;

    if (param.FatherID == 0) {
      param.FatherPath = '|0|';
      param.OriginalUrlPath = param.OriginalUrl;

      const exist = await this.url.findOne({
        where: {
          OriginalUrl: param.OriginalUrl,
          leaf: false,
        },
      });

      if (exist) throw new CoolCommException('Url已存在!');
    } else {
      const data = await this.url.findOneBy({ ID: param.FatherID });
      param.FatherPath = `${data.FatherPath}${param.FatherID}|`;
      param.OriginalUrlPath = `${data.OriginalUrlPath}/${param.OriginalUrl}`;

      await this.url.update(data.ID, {
        leaf: false,
        iconCls: 'folder',
      });
    }

    return await this.url.save(param);
  }

  async urlUpdate(param: any) {
    const father = await this.url.findOne({
      where: { ID: param.FatherID },
    });

    const OriginalUrlPath = father
      ? `${father.OriginalUrlPath}/${param.OriginalUrl}`
      : param.OriginalUrl;

    await this.url.update(param.ID, {
      OriginalUrl: param.OriginalUrl,
      OriginalUrlPath,
      TargetUrl: param.TargetUrl,
      Describe: param.Describe,
    });
  }

  async switchEnable(param: any) {
    await this.url.update(param.id, {
      Enable: param.Enable,
    });
  }

  async urlDelete(param: any) {
    for (const ID of param.IDs) {
      await this.url.update(ID, {
        IsDeleted: true,
      });
    }
  }

  async getUrlFromPath(path: string) {
    return await this.url.findOne({
      where: {
        OriginalUrlPath: path,
        TargetUrl: Not(''),
        IsDeleted: false,
        Enable: true,
      },
    });
  }

  async urlDetailAdd(param: any) {
    const model: any = {
      ForumName: '',
    };

    let userinfo = this.utils.decodeDZAuthkey(this.ctx);
    if (userinfo) {
      userinfo = userinfo.split('\t');
      const uid = parseInt(userinfo[1], 10);
      const member = await this.ccCommonMember.findOne({
        where: {
          uid,
        },
      });

      model.ForumName = member.username;
    }

    const agent = useragent.parse(this.ctx.get('User-Agent'));
    const browserType = `${agent.family}${agent.major}`;
    const platform = agent.os.family;

    model.OriginalUrl = param.OriginalUrl;
    model.TargetUrl = param.TargetUrl;
    model.Referrer = this.ctx.get('Referer') || '';
    model.IPAddress = await this.utils.getReqIP(this.ctx);
    model.BrowserType = browserType;
    model.Platform = platform;
    model.Year = this.utils.today('YYYY');
    model.Month = this.utils.today('YYYY.MM');
    model.Week = this.utils.week('YYYY.MM.DD', 'YYYY.MM.DD');
    model.VisitTime = this.utils.utcTimeStamp().toString();
    model.Date = this.utils.ticksToMoment(this.utils.utcTimeStamp()).toString();
    model.IsDeleted = false;
    model.Resolution = param.Resolution;
    model.Query = param.query;

    return await this.urlDetails.save(model);
  }
}
