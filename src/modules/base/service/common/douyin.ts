import {
  Inject,
  Provide,
  Logger,
  Config,
  Task,
  FORMAT,
  App,
} from '@midwayjs/decorator';
import { BaseService } from '@cool-midway/core';
import { InjectEntityModel } from '@midwayjs/typeorm';
import { Repository, MoreThanOrEqual, In } from 'typeorm';
import { Utils } from '../../../../comm/utils';
import { ILogger } from '@midwayjs/logger';
import { Application, Context } from '@midwayjs/koa';
import { OSSService } from '@midwayjs/oss';
import { DouyinUrlEntity } from '../../entity/douyin/douyinUrl';
// import tingwu20220930, * as $tingwu20220930 from '@alicloud/tingwu20220930';
// import * as $OpenApi from '@alicloud/openapi-client';
// import * as $Util from '@alicloud/tea-util';
import * as _ from 'lodash';
import { BaseSysConfService } from '../sys/conf';
import axios from 'axios';
import * as QRCode from 'qrcode';
import * as fse from 'fs-extra';
import { DouyinAccountEntity } from '../../entity/douyin/douyinAccount';
import { DouyinFollowEntity } from '../../entity/douyin/douyinFollow';
import { DouyinImageEntity } from '../../entity/douyin/douyinImage';
import { DouyinTagEntity } from '../../entity/douyin/douyinTag';
import { DouyinTagUserEntity } from '../../entity/douyin/douyinTagUser';
const { generate_a_bogus } = require('../../../../comm/a_bogus.js');

/**
 * DouYin
 */
@Provide()
export class DouyinService extends BaseService {
  @Inject()
  ctx: Context;

  @App()
  app: Application;

  @InjectEntityModel(DouyinUrlEntity)
  douyinUrlEntity: Repository<DouyinUrlEntity>;

  @InjectEntityModel(DouyinTagEntity)
  douyinTagEntity: Repository<DouyinTagEntity>;

  @InjectEntityModel(DouyinTagUserEntity)
  douyinTagUserEntity: Repository<DouyinTagUserEntity>;

  @InjectEntityModel(DouyinFollowEntity)
  douyinFollowEntity: Repository<DouyinFollowEntity>;

  @InjectEntityModel(DouyinAccountEntity)
  douyinAccountEntity: Repository<DouyinAccountEntity>;

  @InjectEntityModel(DouyinImageEntity)
  douyinImageEntity: Repository<DouyinImageEntity>;

  @Inject()
  ossService: OSSService;

  @Inject()
  baseSysConfService: BaseSysConfService;

  @Inject()
  utils: Utils;

  @Inject()
  baseDir;

  @Logger()
  logger: ILogger;

  @Config('oss')
  oss;

  @Config('tingwu')
  tingwu;

  @Config('douyin')
  douyin;

  async clearFollow(followId: number) {
    const urls = await this.douyinUrlEntity.find({
      where: {
        followId,
      },
    });

    for (const url of urls) {
      if (url.localFile.length) {
        await fse.remove(`${this.baseDir}/..${url.localFile}`);
        await fse.remove(
          `${this.baseDir}/..${url.localFile.replace('.mp4', '.jpeg')}`
        );
      }

      await this.douyinUrlEntity.delete(url.id);
    }
  }

  async urlPage(param: any) {
    const { id, s, url, author, title } = param;

    const sql = `
        SELECT a.*, b.username FROM douyin_url as a 
        LEFT OUTER JOIN base_sys_user as b 
        ON a.uid = b.id
        WHERE a.uid = ${this.ctx.admin.userId} 
            ${this.setSql(!_.isEmpty(id), 'and a.id=?', id)}
            ${this.setSql(!_.isEmpty(url), 'and a.url like ?', `%${url}%`)}
            ${this.setSql(
              !_.isEmpty(author),
              'and a.author like ?',
              `%${author}%`
            )}
            ${this.setSql(
              !_.isEmpty(title),
              'and a.title like ?',
              `%${title}%`
            )}
            ${this.setSql(
              !_.isEmpty(s),
              'and (a.title like ? or a.author like ?)',
              [`%${s}%`, `%${s}%`]
            )}
            and a.status > -3
            ORDER BY a.videoDate DESC`;
    return this.sqlRenderPage(sql, param, false);
  }

  async userPage(param: any) {
    const { id, author } = param;

    const sql = `
        SELECT a.*, b.username FROM douyin_follow as a 
        LEFT OUTER JOIN base_sys_user as b 
        ON a.uid = b.id
        WHERE a.status >=-2 and a.uid = ${this.ctx.admin.userId} 
            ${this.setSql(!_.isEmpty(id), 'and a.id=?', id)}
            ${this.setSql(
              !_.isEmpty(author),
              'and a.author like ?',
              `%${author}%`
            )}
            
            ORDER BY a.id DESC`;
    return this.sqlRenderPage(sql, param, false);
  }

  async userUpdate(param: any) {
    if (param.before == 1) {
      param.status = -1;
    }

    const relDel = param.relDel;
    delete param.relDel;

    await this.douyinFollowEntity.update(param.id, param);

    if (param.status == -3 && relDel) {
      await this.douyinUrlEntity.update(
        {
          followId: param.id,
        },
        {
          status: -3,
        }
      );
    }
  }

  async tagUserList(param: any) {
    const uid = this.ctx.admin.userId;
    const take = param.take || 15;
    const skip = param.skip || 1;

    const where: any = {
      uid,
    };

    if (param.tagId) {
      where.tagId = param.tagId;
    }

    const res = await this.douyinTagUserEntity.findAndCount({
      where,
      take,
      skip: (skip - 1) * take,
    });

    for (let tag of res[0]) {
      // @ts-ignore
      tag.follow = await this.douyinFollowEntity.find({
        where: {
          id: tag.followId,
          status: MoreThanOrEqual(-2),
        },
      });
    }

    return res;
  }

  async tagUserAll(param: any) {
    const uid = this.ctx.admin.userId;

    const all = await this.douyinTagUserEntity.find({
      where: {
        uid,
        followId: param.followId,
      },
    });

    const tagIds = all.map(row => row.tagId);

    return await this.douyinTagEntity.find({
      where: {
        uid,
        id: In(tagIds),
      },
    });
  }

  async tagUserCreate(param: any) {
    const uid = this.ctx.admin.userId;

    await this.douyinTagUserEntity.save({
      uid,
      tagId: param.tagId,
      followId: param.followId,
    });
  }

  async tagUserDelete(param: any) {
    const uid = this.ctx.admin.userId;

    await this.douyinTagUserEntity.delete({
      uid,
      tagId: param.tagId,
      followId: param.followId,
    });
  }

  async tagPage(param: any) {
    const { id, name } = param;

    const sql = `
        SELECT * FROM douyin_tag 
        WHERE uid = ${this.ctx.admin.userId} 
            ${this.setSql(!_.isEmpty(id), 'and id=?', id)}
            ${this.setSql(!_.isEmpty(name), 'and name = ?', `${name}`)}
            ORDER BY id DESC`;
    return this.sqlRenderPage(sql, param, false);
  }

  async tagCreate(param: any) {
    const uid = this.ctx.admin.userId;

    await this.douyinTagEntity.save({
      uid,
      name: param.name,
    });
  }

  async tagUpdate(param: any) {
    await this.douyinTagEntity.update(param.id, param);
  }

  async tagDelete(param: any) {
    await this.douyinTagEntity.delete(param.id);
  }

  async urlCreate(param: any) {
    const { id, type } = await this.utils.douyinIdParse(param.url);

    if (type === 'user') throw new Error('不能添加User类型');

    const url = `https://www.douyin.com/${type}/${id}`;
    const uid = this.ctx.admin.userId;

    return await this.douyinUrlEntity.save({
      uid,
      url,
      run: 1,
    });
  }

  async urlUpdate(param: any) {
    await this.douyinUrlEntity.update(param.id, param);
  }

  async urlImages(param: any) {
    return await this.douyinImageEntity.find({
      where: {
        urlId: param.urlId,
      },
    });
  }

  async homeCreate(param: any) {
    const model = {
      uid: this.ctx.admin.userId,
      dyUid: param.dyUid,
      author: param.author,
      before: param.before || 0,
      after: param.after || 0,
      interval: param.interval || 300,
      status: -1,
    };

    if (model.after === 1 && model.before === 0) {
      model.status = 0;
    }

    await this.douyinFollowEntity.save(model);
  }

  async urlHomeCreate(param: any) {
    const urls = param.urls;
    const uid = this.ctx.admin.userId;
    const arr = [];

    for (let url of urls) {
      arr.push({
        uid,
        url: `https://www.douyin.com/video/${url.aweme_id}`,
        title: url.desc,
        targetUrl: url.url,
        remoteCoverUrl: url.cover,
      });
    }

    if (arr.length === 0) return;
    await this.douyinUrlEntity.save(arr);
  }

  async urlDelete(param: any) {
    const where: any = {
      id: param.id,
    };

    if (!this.utils.isAdmin(this.ctx)) {
      where.uid = this.ctx.admin.userId;
    }

    await this.douyinUrlEntity.delete(where);
  }

  async douyinHome(param: any) {
    const accounts = await this.douyinAccountEntity.find();

    const idx = Math.floor(Math.random() * accounts.length);
    const account = accounts[idx];

    const cookie = account.cookie;
    let uid = await this.utils.douyinUserUrlParse(param.url, cookie);

    const max_cursor = param.max_cursor || 0;

    const webid = await this.utils.get_webid();
    const msToken = await this.utils.get_msToken();
    const verifyFp = this.utils.get_verify_fp();

    const query = `device_platform=webapp&aid=6383&channel=channel_pc_web&sec_user_id=${uid}&max_cursor=${max_cursor}&show_live_replay_strategy=1&need_time_list=1&time_list_query=0&whale_cut_token=&cut_version=1&count=35&publish_video_strategy_type=2&update_version_code=170400&pc_client_type=1&version_code=290100&version_name=29.1.0&cookie_enabled=true&screen_width=2560&screen_height=1440&browser_language=zh-CN&browser_platform=MacIntel&browser_name=Chrome&browser_version=*********&browser_online=true&engine_name=Blink&engine_version=*********&os_name=Mac+OS&os_version=10.15.7&cpu_core_num=6&device_memory=8&platform=PC&downlink=10&effective_type=4g&round_trip_time=50&webid=${webid}&msToken=${msToken}`;
    const a_bogus = generate_a_bogus(query, this.douyin.agent);
    const url = `https://www.douyin.com/aweme/v1/web/aweme/post/?${query}&a_bogus=${a_bogus}&verifyFp=${verifyFp}&fp=${verifyFp}`;

    return await this.utils.douyinHomeInfo(url, cookie);
  }

  async douyinUrlParse(param: any) {
    const { url } = param;
    let { id, type } = await this.utils.douyinIdParse(url);

    const accounts = await this.douyinAccountEntity.find();

    const idx = Math.floor(Math.random() * accounts.length);
    const account = accounts[idx];
    const cookie = account.cookie;

    if (param.home) {
      let res = (await this.utils.douyinUrlParse(id, cookie)) || {};
      let author = res?.aweme_detail?.author?.nickname;
      id = res?.aweme_detail?.author?.sec_uid;

      return {
        type: 'user',
        id,
        author,
      };
    }

    if (type === 'user') {
      const home = await this.douyinHome({
        url,
      });

      const author = home?.aweme_list[0]?.author?.nickname;

      return {
        type: 'user',
        id,
        author,
      };
    }

    if (type === 'ixigua') {
      let res = await this.parseXiguaUrl(
        `https://m.ixigua.com/douyin/share/video/${id}?aweme_type=107&schema_type=1&utm_source=copy&utm_campaign=client_share&utm_medium=android&app=aweme`
      );

      const regex = /_ROUTER_DATA\s*=\s*(\{.*?\})(?=<\/script>)/;
      const match = res.match(regex);

      if (match && match[1]) {
        let data = JSON.parse(match[1]);

        const item =
          data.loaderData['video_(id)/page'].videoInfoRes.item_list[0];

        item.video.play_addr.url_list[0] =
          item.video.play_addr.url_list[0].replace(
            /\\u([\dA-F]{4})/gi,
            (match, grp) => {
              return String.fromCharCode(parseInt(grp, 16));
            }
          );
        item.video.cover.url_list[0] = item.video.cover.url_list[0].replace(
          /\\u([\dA-F]{4})/gi,
          (match, grp) => {
            return String.fromCharCode(parseInt(grp, 16));
          }
        );

        return {
          aweme_detail: item,
          type,
        };
      } else {
        return {
          aweme_detail: '',
          type,
        };
      }
    }

    let res = (await this.utils.douyinUrlParse(id, cookie)) || {};
    res.type = type;

    if (type === 'note') {
      return res;
    }

    if (res.aweme_detail) {
      const url = res.aweme_detail.video.play_addr.url_list.find((el: string) =>
        el.includes('v1/play')
      );
      const realUrl = await this.utils.douyinRealUrl(url);

      const hrefRegex = /<a href="(.*?)">/;

      const match = realUrl.match(hrefRegex);
      if (match && match[1]) {
        res.aweme_detail.realUrl = match[1];
      }
    }

    return res;
  }

  async douyinUrlDownload(param: any) {
    const { url } = param;

    let res = await this.utils.downloadDouyin(url);

    return {
      file: res,
    };
  }

  async urlRedo(param: any) {
    const { id } = param;

    await this.douyinUrlEntity.update(
      {
        id,
      },
      {
        status: 0,
        message: '',
      }
    );
  }

  async parseXiguaUrl(url) {
    const res = await axios.get(url, {
      headers: {
        'User-Agent':
          'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/79.0.3945.88 Safari/537.36',
        Referer: 'https://www.ixigua.com/',
        Cookie:
          'MONITOR_WEB_ID=7892c49b-296e-4499-8704-e47c1b150c18; ixigua-a-s=1; ttcid=af99669b6304453480454f150701d5c226; BD_REF=1; __ac_nonce=060d88ff000a75e8d17eb; __ac_signature=_02B4Z6wo00f01kX9ZpgAAIDAKIBBQUIPYT5F2WIAAPG2ad; ttwid=1%7CcIsVF_3vqSIk4XErhPB0H2VaTxT0tdsTMRbMjrJOPN8%7C1624806049%7C08ce7dd6f7d20506a41ba0a331ef96a6505d96731e6ad9f6c8c709f53f227ab1',
      },
      maxRedirects: 0,
      validateStatus: status => {
        return status >= 200 && status < 400;
      },
    });

    return res.data;
  }

  async qrCodeUrl() {
    const verifyFp = this.utils.get_verify_fp();
    const ttWid = await this.get_tt_wid();

    let url = `${this.douyin.ssologin.qr}service=https%3A%2F%2Fwww.douyin.com&need_logo=false&need_short_url=true&device_platform=web_app&aid=6383&account_sdk_source=sso&sdk_version=2.2.5&language=zh&verifyFp=${verifyFp}&fp=${verifyFp}`;
    url = this.utils.douyinSignUrl(url, this.douyin.agent);

    const response = await axios.get(url, {
      headers: {
        'User-Agent': this.douyin.agent,
        Referer: this.douyin.referer,
        Cookie: ttWid,
      },
      maxRedirects: 0,
      validateStatus: status => {
        return status >= 200 && status < 400;
      },
    });

    const image = await QRCode.toDataURL(response.data.data.qrcode_index_url);
    const cookie = response.headers['set-cookie'].map((el: any) => {
      return el.split(';')[0];
    });
    cookie.push(ttWid);

    return {
      image,
      verifyFp,
      ttWid,
      cookie,
      ...response.data.data,
    };
  }

  async checkQrCode(param: any) {
    let url = `${this.douyin.ssologin.checkQr}token=${param.token}&service=https%3A%2F%2Fwww.douyin.com&need_logo=false&need_short_url=true&device_platform=web_app&aid=6383&account_sdk_source=sso&sdk_version=2.2.5&language=zh&verifyFp=${param.verifyFp}&fp=${param.verifyFp}`;
    url = this.utils.douyinSignUrl(url, this.douyin.agent);

    const response = await axios.get(url, {
      headers: {
        'User-Agent': this.douyin.agent,
        Referer: this.douyin.referer,
        Cookie: param.cookie,
      },
      maxRedirects: 0,
      validateStatus: status => {
        return status >= 200 && status < 400;
      },
    });

    const cookie = response.headers['set-cookie'].map((el: any) => {
      return el.split(';')[0];
    });

    return {
      data: response.data.data,
      cookie,
    };
  }

  async loginQrCode(param: any) {
    let response = await axios.get(param.redirect_url, {
      headers: {
        'User-Agent': this.douyin.agent,
        Referer: this.douyin.referer,
        Cookie: param.cookie,
      },
      maxRedirects: 0,
      validateStatus: status => {
        return status >= 200 && status < 400;
      },
    });

    if (response.status === 302) {
      response = await axios.get(response.headers.location, {
        headers: {
          'User-Agent': this.douyin.agent,
          Referer: this.douyin.referer,
          Cookie: param.cookie,
        },
        maxRedirects: 0,
        validateStatus: status => {
          return status >= 200 && status < 400;
        },
      });

      const cookie = response.headers['set-cookie'].map((el: any) => {
        return el.split(';')[0];
      });

      await this.douyinAccountEntity.update(
        {
          id: param.id,
        },
        {
          exp: this.utils.now() + 60 * 60 * 24 * 7,
          cookie: cookie.join(';'),
        }
      );
    } else {
      throw new Error('获取抖音Cookie跳转失败！');
    }
  }

  async account() {
    return await this.douyinAccountEntity.find();
  }

  async accountCookie(param: any) {
    await this.douyinAccountEntity.update(param.id, {
      cookie: param.cookie,
    });
  }

  async accountCreate(param: any) {
    return await this.douyinAccountEntity.save(param);
  }

  async accountDelete(param: any) {
    return await this.douyinAccountEntity.delete({
      id: param.id,
    });
  }

  async accountSwitch(param: any) {
    await this.douyinAccountEntity.update(
      {},
      {
        master: 0,
      }
    );
    await this.douyinAccountEntity.update(
      {
        id: param.id,
      },
      {
        master: 1,
      }
    );
  }

  async accountStatus(param: any) {
    await this.douyinAccountEntity.update(
      {
        id: param.id,
      },
      {
        status: param.status,
      }
    );
  }

  async get_tt_wid() {
    const headers = {
      'User-Agent': this.douyin.agent,
    };
    const api = 'https://ttwid.bytedance.com/ttwid/union/register/';
    const data = {
      region: 'cn',
      aid: 1768,
      needFid: false,
      service: 'www.ixigua.com',
      migrate_info: {
        ticket: '',
        source: 'node',
      },
      cbUrlProtocol: 'https',
      union: true,
    };

    try {
      const response = await axios.post(api, data, {
        headers: headers,
        timeout: 10000,
      });

      const s = response.headers['set-cookie'];
      return s[0].split(';', 1)[0];
    } catch (error) {
      throw new Error('提取 ttwid 参数失败！');
    }
  }

  // async sign(param) {
  //   const webid = await this.get_webid();
  //   const msToken = await this.get_msToken();

  //   const query = `device_platform=webapp&aid=6383&channel=channel_pc_web&update_version_code=170400&pc_client_type=1&version_code=190500&version_name=19.5.0&cookie_enabled=true&screen_width=2560&screen_height=1440&browser_language=zh-CN&browser_platform=MacIntel&browser_name=Chrome&browser_version=*********&browser_online=true&engine_name=Blink&engine_version=*********&os_name=Mac+OS&os_version=10.15.7&cpu_core_num=6&device_memory=8&platform=PC&downlink=10&effective_type=4g&round_trip_time=50&webid=${webid}&msToken=${msToken}&aweme_id=${param.aweme_id}`;
  //   const a_bogus = generate_a_bogus(query, this.douyin.agent);
  //   const verifyFp = this.get_verify_fp();

  //   const url = `https://www.douyin.com/aweme/v1/web/aweme/detail/?${query}&a_bogus=${a_bogus}&verifyFp=${verifyFp}&fp=${verifyFp}`;

  //   return url;
  // }

  // async getTransResult() {
  //   const urls = await this.douyinUrlEntity.find({
  //     where: {
  //       status: 1,
  //     },
  //   });

  //   let config = new $OpenApi.Config({
  //     accessKeyId: this.oss.client.accessKeyId,
  //     accessKeySecret: this.oss.client.accessKeySecret,
  //     endpoint: this.tingwu.endpoint,
  //   });

  //   const client = new tingwu20220930(config);
  //   let runtime = new $Util.RuntimeOptions({});
  //   let headers: { [key: string]: string } = {};

  //   for (const url of urls) {
  //     try {
  //       const res = await client.getFileTransWithOptions(
  //         url.transId,
  //         headers,
  //         runtime
  //       );

  //       if (res.body.code == '0' && res.body.data.transStatus === 'COMPLETED') {
  //         let result = '';
  //         const res = await this.ossService.get(url.remoteFilePath);
  //         const txt = JSON.parse(res.content?.toString());

  //         const pgs =
  //           (txt.DocResult?.pg && Object.values(txt.DocResult.pg)) || [];

  //         for (const pg of pgs) {
  //           // @ts-ignore
  //           for (const el of pg.sc) {
  //             result += el.tc;
  //           }
  //         }

  //         await this.douyinUrlEntity.update(
  //           {
  //             id: url.id,
  //           },
  //           {
  //             txt: result,
  //             status: 2,
  //           }
  //         );
  //       }
  //     } catch (err) {
  //       this.logger.error(err.message);
  //     }
  //   }
  // }
}
