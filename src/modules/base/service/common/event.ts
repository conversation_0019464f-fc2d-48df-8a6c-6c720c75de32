import {
  Inject,
  Provide,
  Logger,
  InjectClient,
  Config,
} from '@midwayjs/decorator';
import { BaseService } from '@cool-midway/core';
import { InjectEntityModel } from '@midwayjs/typeorm';
import { In, LessThanOrEqual, Like, MoreThanOrEqual, Repository, Raw } from 'typeorm';
import { Utils } from '../../../../comm/utils';
import { ILogger } from '@midwayjs/logger';
import { Context } from '@midwayjs/koa';
import { TblEvent } from '../../entity/event/TblEvent';
import * as _ from 'lodash';
import { TblSchoolDic } from '../../entity/event/TblSchoolDic';
import { TblGeo } from '../../entity/event/TblGeo';
import { TblEventTypeDict } from '../../entity/event/TblEventTypeDict';
import { TblSchoolMajorDic } from '../../entity/event/TblSchoolMajorDic';
import { TblUpload } from '../../entity/event/TblUpload';
import { TblEventLocation } from '../../entity/event/TblEventLocation';
import { TblEventSchoolMajor } from '../../entity/event/TblEventSchoolMajor';
import { TblMajorCategoryDic } from '../../entity/event/TblMajorCategoryDic';
import { TblEventMajor } from '../../entity/event/TblEventMajor';
import { TblEventType } from '../../entity/event/TblEventType';
import { TblEventCalendar } from '../../entity/event/TblEventCalendar';
import { TblEventRelease } from '../../entity/event/TblEventRelease';
import { IwmsNews } from '../../entity/www/IwmsNews';
import { TblEventUser } from '../../entity/event/TblEventUser';
import { CommonForumService } from './forum';
import { CachingFactory, MidwayCache } from '@midwayjs/cache-manager';
import { TblSystemSettings } from '../../entity/event/TblSystemSettings';
import { TblSchoolAreaDic } from '../../entity/event/TblSchoolAreaDic';
import { TblEventApplyCalendar } from '../../entity/event/TblEventApply';
import { EventType } from '../types/enum';
import { TblEventExternalLink } from '../../entity/event/TblEventExternalLink';
import { CcCommonMember } from '../../entity/forum/CcCommonMember';
import * as useragent from 'useragent';
import { TblEventExternalLinkLog } from '../../entity/event/TblEventExternalLinkLog';
import { TblEventIESE } from '../../entity/event/TblEventIESE';
import { BlogEntity } from '../../entity/iese/blog';
import { BlogTagEntity } from '../../entity/iese/blogTag';
import { BlogTagsEntity } from '../../entity/iese/blogTags';
import { PortalArticleEntity } from '../../entity/portal/Article';
import { CoolElasticSearch } from '@cool-midway/es';
const fs = require('fs');

/**
 * Event
 */
@Provide()
export class EventService extends BaseService {
  @Inject()
  ctx: Context;

  @Inject()
  es: CoolElasticSearch;

  @InjectEntityModel(TblEvent, 'id')
  tblEvent: Repository<TblEvent>;

  @InjectEntityModel(TblEventIESE, 'id')
  tblEventIESE: Repository<TblEventIESE>;

  @InjectEntityModel(TblEventLocation, 'id')
  tblEventLocation: Repository<TblEventLocation>;

  @InjectEntityModel(TblEventSchoolMajor, 'id')
  tblEventSchoolMajor: Repository<TblEventSchoolMajor>;

  @InjectEntityModel(TblEventMajor, 'id')
  tblEventMajor: Repository<TblEventMajor>;

  @InjectEntityModel(TblSchoolDic, 'id')
  tblSchoolDic: Repository<TblSchoolDic>;

  @InjectEntityModel(TblEventType, 'id')
  tblEventType: Repository<TblEventType>;

  @InjectEntityModel(TblEventTypeDict, 'id')
  tblEventTypeDict: Repository<TblEventTypeDict>;

  @InjectEntityModel(TblEventCalendar, 'id')
  tblEventCalendar: Repository<TblEventCalendar>;

  @InjectEntityModel(TblEventRelease, 'id')
  tblEventRelease: Repository<TblEventRelease>;

  @InjectEntityModel(TblEventUser, 'id')
  tblEventUser: Repository<TblEventUser>;

  @InjectEntityModel(TblSchoolMajorDic, 'id')
  tblSchoolMajorDic: Repository<TblSchoolMajorDic>;

  @InjectEntityModel(TblMajorCategoryDic, 'id')
  tblMajorCategoryDic: Repository<TblMajorCategoryDic>;

  @InjectEntityModel(TblEventApplyCalendar, 'id')
  tblEventApplyCalendar: Repository<TblEventApplyCalendar>;

  @InjectEntityModel(TblUpload, 'id')
  tblUpload: Repository<TblUpload>;

  @InjectEntityModel(TblGeo, 'id')
  tblGeo: Repository<TblGeo>;

  @InjectEntityModel(TblSystemSettings, 'id')
  tblSystemSettings: Repository<TblSystemSettings>;

  @InjectEntityModel(TblSchoolAreaDic, 'id')
  tblSchoolAreaDic: Repository<TblSchoolAreaDic>;

  @InjectEntityModel(TblEventExternalLink, 'id')
  tblEventExternalLink: Repository<TblEventExternalLink>;

  @InjectEntityModel(TblEventExternalLinkLog, 'id')
  tblEventExternalLinkLog: Repository<TblEventExternalLinkLog>;

  @InjectEntityModel(BlogEntity)
  blogEntity: Repository<BlogEntity>;

  @InjectEntityModel(BlogTagEntity)
  blogTagEntity: Repository<BlogTagEntity>;

  @InjectEntityModel(BlogTagsEntity)
  blogTagsEntity: Repository<BlogTagsEntity>;

  @InjectEntityModel(PortalArticleEntity)
  portalArticleEntity: Repository<PortalArticleEntity>;

  @InjectEntityModel(IwmsNews, 'www')
  iwmsNews: Repository<IwmsNews>;

  @InjectEntityModel(CcCommonMember, 'forum')
  ccCommonMember: Repository<CcCommonMember>;

  @Inject()
  commonForumService: CommonForumService;

  @Logger()
  logger: ILogger;

  @Inject()
  utils: Utils;

  @Inject()
  baseDir;

  @Config('redisKey')
  redisKey;

  @InjectClient(CachingFactory, 'default')
  midwayCache: MidwayCache;

  async ieseBlogs(page, page_size, tag_id) {    
    const pageSize = page_size;    

    const queryBuilder = this.blogEntity
        .createQueryBuilder('blog')
        .leftJoinAndSelect('blog.tags', 'tags')
        .where('blog.status = :status', { status: 0 });

    if (tag_id) {
        queryBuilder.andWhere('tags.tagId = :tagId', { tagId: tag_id });
    }

    const res = await queryBuilder
        .skip((page - 1) * pageSize)
        .take(pageSize)
        .orderBy('blog.datetime', 'DESC')
        .addOrderBy('blog.id', 'DESC')
        .getManyAndCount();
        
    return {
      rows: res[0],
      count: res[1],
    };
  }

  async blogShow(uuid: any){
    const res: any = await this.blogEntity.findOne({
      relations: ['tags'],
      where: { uuid },
    });

    if(res?.tags?.length) {
      const tagIds = res.tags.map(el=>el.tagId);

      res.tags = await this.blogTagsEntity.find({
        where: {
          id: In(tagIds)
        }
      })
    }

    return res;
  }

  async ieseEvents(page, page_size, datetime, majors, locations) {    
    const pageSize = page_size;
    let one_day = 60 * 60 * 24

    const where: any = {        
      status: 0,
    }
    
    majors = Array.isArray(majors) ? majors : (majors ? [majors] : []);    
    locations = Array.isArray(locations) ? locations : (locations ? [locations] : []);

    if(datetime) {
      where.push_end_date = MoreThanOrEqual(this.utils.now() + one_day * parseInt(datetime, 10));
    }
    
    if(majors.length) {
      where.major = Raw(alias => `${alias} REGEXP '${majors.join('|')}'`);
    }
    
    if(locations.length) {
      where.location_id = In(locations);
    }

    const res = await this.tblEventIESE.findAndCount({
      relations: ['event_geo'],
      where,
      skip: (page - 1) * pageSize,
      take: pageSize,
      order: {
        event_begin_date: 'ASC',
      },
    });

    return {
      rows: res[0],
      count: res[1],
    }
  }

  async eventShow(id: any){
    return await this.tblEventIESE.findOneBy({id});
  }

  async searchItems() {
    const now = this.utils.now();

    const res = await this.tblEventIESE
      .createQueryBuilder('event')
      .select('event.location_id')
      .where('event.status = :status', { status: 0 })
      .andWhere('event.push_begin_date <= :now', { now })
      .andWhere('event.push_end_date >= :now', { now })
      .groupBy('event.location_id')
      .getRawMany();
    
    const locations = await this.tblGeo.find({
      where: {
        id: In(res.map(item => item.event_location_id)),
      },
    });

    return {locations, "majors": [{id: 116, name: 'MBA'}, {id: 220, name: 'MiM'}]};
  }

  async ieseList(param: any) {
    const page = param.page || 1;
    const pageSize = param.pageSize || 50;    

    const where: any = {};

    if(param.status == -1) {
      where.status = -1;
    } else {
      where.status = MoreThanOrEqual(0);
    }

    return await this.tblEventIESE.findAndCount({
      relations: ['event_geo'],
      where,
      skip: (page - 1) * pageSize,
      take: pageSize,
      order: { id: 'DESC' },
    });
  }
  
  async ieseAdd(param: any) {
    param.created_at = this.utils.now();
    param.major = param.major.sort().join(',');
    param.location_id = param.city_id || param.province_id;
    param.city_id = param.city_id || 0;

    return await this.tblEventIESE.save(param);
  }

  async ieseUpdate(param: any) {
    await this.tblEventIESE.update(
      {
        id: param.id,
      },
      {
        subject: param.subject,
        content: this.utils.replaceLinksForPush(param.content, 'iesecn'),
        event_begin_date: param.event_begin_date,        
        event_end_date: param.event_end_date,        
        push_begin_date: param.push_begin_date,        
        push_end_date: param.push_end_date,                
        location_id: param.city_id || param.province_id,
        country_id: param.country_id || 0,
        province_id: param.province_id || 0,
        city_id: param.city_id || 0,
        major: param.major.sort().join(',')
      }
    );
  }

  async ieseDelete(param: any) {
    await this.tblEventIESE.delete({ id: param.id });
  }

  async acList(param: any) {
    const page = param.page || 1;
    const pageSize = param.pageSize || 50;

    return await this.tblEventApplyCalendar.findAndCount({
      where: {},
      skip: (page - 1) * pageSize,
      take: pageSize,
      order: { id: 'DESC' },
    });
  }

  async acUpdate(param: any) {
    await this.tblEventApplyCalendar.update(
      {
        id: param.id,
      },
      {
        subject: param.subject,
        content: param.content,
        author: param.author,
      }
    );
  }

  async offline(param: any) {
    if (param.type == 1) {
      await this.tblEventCalendar.update(
        {
          id: param.id,
        },
        {
          status: -1,
        }
      );
    } else if (param.type == 2) {
      await this.tblEventRelease.update(
        {
          id: param.id,
        },
        {
          status: -1,
        }
      );

      await this.loadReleaseToCache();
    }
  }

  async updateTotalDisplayCount(param: any) {
    await this.tblEvent.update(
      {
        id: param.id,
      },
      {
        total_display_count: param.total_display_count,
      }
    );
  }

  async updateTotalRedirectCount(param: any) {
    await this.tblEvent.update(
      {
        id: param.id,
      },
      {
        total_redirect_count: param.total_redirect_count,
      }
    );
  }

  async externalsLog(param: any) {
    const page = param.page || 1;
    const pageSize = param.pageSize || 30;

    return await this.tblEventExternalLinkLog.findAndCount({
      where: {
        external_id: param.external_id,
        type: param.type,
      },
      skip: (page - 1) * pageSize,
      take: pageSize,
    });
  }

  async externals(param: any) {
    return await this.tblEventExternalLink.find({
      where: {
        event_id: param.eventId,
      },
    });
  }

  async getUrlFromPath(path: string) {
    return await this.tblEventExternalLink.findOne({
      where: {
        shortUrl: path,
      },
    });
  }

  async externalLinkLogAdd(param: any) {
    const model: any = {
      external_id: param.external_id,
      type: param.type,
      username: '',
      ip: await this.utils.getReqIP(this.ctx),
      referrer: this.ctx.get('Referer') || '',
      useragent: this.ctx.get('User-Agent'),
      resolution: param.resolution || '',
      created_at: this.utils.now(),
    };

    let userinfo = this.utils.decodeDZAuthkey(this.ctx);
    if (userinfo) {
      userinfo = userinfo.split('\t');
      const uid = parseInt(userinfo[1], 10);
      const member = await this.ccCommonMember.findOne({
        where: {
          uid,
        },
      });

      model.username = member.username;
    }

    await this.tblEventExternalLinkLog.save(model);

    const num = await this.tblEventExternalLinkLog.count({
      where: {
        external_id: param.external_id,
        type: param.type,
      },
    });

    const field: any = {};

    if (param.type === 'p') {
      field.portalNum = num;
    } else if (param.type === 'f') {
      field.forumNum = num;
    } else if (param.type === 'c') {
      field.calendarNum = num;
    } else if (param.type === 'wx') {
      field.wechatNum = num;
    } else if (param.type === 'hb') {
      field.hbNum = num;
    } else if (param.type === 'iesecn') {
      field.ieseNum = num;
    } else {
      field.otherNum = num;
    }

    await this.tblEventExternalLink.update(
      {
        id: param.external_id,
      },
      field
    );
  }

  async publishToApply(param: any) {
    param.event_id = param.eventId;
    param.created_at = this.utils.now();
    param.content = this.utils.replaceLinksForPush(param.content, 'c');
    delete param.eventId;

    const res = await this.tblEventApplyCalendar.save(param);
    await this.tblEvent.update(
      { id: param.event_id },
      {
        apply_url: res.id,
      }
    );
  }

  async publishToIESE(param: any) {        
    for(const location of param.locations){
      const res = await this.tblEventIESE.save({
        event_id: param.eventId,
        event_begin_date: location.event_begin_date,
        event_end_date: location.event_end_date,
        push_begin_date: location.push_begin_date,
        push_end_date: location.push_end_date,        
        subject: location.subject,
        content: this.utils.replaceLinksForPush(param.content, 'iesecn'),
        major: location.school_major.sort().join(','),        
        location_id: location.event_geo.id,
        country_id: location.country_id,
        province_id: location.province_id,
        city_id: location.city_id,

        created_at: this.utils.now()
      });

      await this.tblEvent.update(
        { id: param.eventId },
        {
          iese_url: parseInt(res.id, 10),
        }
      );
    }
  }

  async showCalendar(param: any) {
    const { page = 1, page_size = 50 } = param;
    const skip = (page - 1) * page_size;

    const oneDay = 60 * 60 * 24;
    const now = this.utils.now();

    const where: any = {
      status: MoreThanOrEqual(0),
      push_begin_date: LessThanOrEqual(now),
      push_end_date: MoreThanOrEqual(now),
    };

    if (param.push_end_date) {
      where.push_end_date = LessThanOrEqual(
        now + oneDay * parseInt(param.push_end_date, 10)
      );
    }

    if (param.locations) {
      where.location_id = In(param?.locations?.trim(',')?.split(','));
    }

    const queryBuilder = this.tblEventCalendar
      .createQueryBuilder('event')
      .leftJoinAndSelect(
        'event.event_school',
        'school',
        'school.id = event.school_id'
      )
      .leftJoinAndSelect(
        'event.event_location',
        'location',
        'location.id = event.lid'
      )
      .leftJoinAndSelect('event.event_type', 'type', 'type.lid = event.lid')
      .leftJoinAndSelect('event.event_geo', 'geo', 'geo.id = event.location_id')
      .where(where)
      .orderBy('event.event_begin_date', 'ASC')
      .skip(skip)
      .take(page_size);

    if (param.majors) {
      queryBuilder.innerJoinAndSelect(
        'event.event_major',
        'major',
        'major.major_id IN (:...majors)',
        {
          majors: param?.majors?.trim(',')?.split(','),
        }
      );
    }

    const [events, count] = await queryBuilder.getManyAndCount();

    if (!param.majors) {
      for (let event of events) {
        event.event_major = await this.tblEventMajor.find({
          where: {
            lid: event.lid.toString(),
          },
        });
      }
    }

    return {
      rows: events,
      count: count,
    };
  }

  async showRelease() {
    let event_list: any =
      (await this.midwayCache.get(`${this.redisKey.eventList}`)) || '';

    if (!event_list) {
      event_list = await this.loadReleaseToCache();
    } else {
      event_list = JSON.parse(event_list);
    }

    const events_count = await this.tblSystemSettings.findOne({
      select: ['value'],
      where: {
        name: 'push1_count',
      },
    });

    event_list.events_count = events_count.value;

    return event_list;
  }

  async calendarClean() {
    const events = await this.tblEventCalendar.find({
      where: {
        status: MoreThanOrEqual(0),
      },
    });

    for (const event of events) {
      if (event.event_end_date < this.utils.now()) {
        await this.tblEventCalendar.update(
          {
            id: event.id,
          },
          {
            status: -1,
          }
        );
      }
    }
  }

  async releaseClean() {
    const events = await this.tblEventRelease.find({
      where: {
        status: MoreThanOrEqual(0),
      },
    });

    for (const event of events) {
      if (
        event.event_end_date < this.utils.now() &&
        event.push_end_date < this.utils.now()
      ) {
        await this.tblEventRelease.update(
          {
            id: event.id,
          },
          {
            status: -1,
          }
        );
      }
    }

    await this.loadReleaseToCache();
  }

  async ieseClean() {
    const events = await this.tblEventIESE.find({
      where: {
        status: MoreThanOrEqual(0),
      },
    });

    for (const event of events) {
      if (
        event.event_end_date < this.utils.now() &&
        event.push_end_date < this.utils.now()
      ) {
        await this.tblEventIESE.update(
          {
            id: event.id,
          },
          {
            status: -1,
          }
        );
      }
    }    
  }

  async loadReleaseToCache() {
    let push1 = await this.tblEventRelease
      .createQueryBuilder('eventRelease')
      .leftJoinAndSelect(
        'eventRelease.event_geo',
        'eventGeo',
        'eventGeo.id IS NOT NULL'
      )
      .addSelect(['eventGeo.id', 'eventGeo.name'])
      .where('eventRelease.status >= :status', { status: 0 })
      .andWhere('eventRelease.type = :type', { type: 1 })
      .andWhere('eventRelease.push_begin_date <= :now', {
        now: this.utils.now(),
      })
      .andWhere('eventRelease.push_end_date >= :now', { now: this.utils.now() })
      .orderBy('eventRelease.event_begin_date', 'ASC')
      .getMany();
    
    const push2_3 = await this.tblEventRelease
      .createQueryBuilder('eventRelease')
      .leftJoinAndSelect('eventRelease.event_geo', 'eventGeo')
      .addSelect(['eventGeo.id', 'eventGeo.name'])
      .where('eventRelease.status >= :status', { status: 0 })
      .andWhere('eventRelease.type IN (:...types)', { types: [2, 3] })
      .andWhere('eventRelease.event_begin_date <= :now', {
        now: this.utils.now(),
      })
      .andWhere('eventRelease.event_end_date >= :now', {
        now: this.utils.now(),
      })
      .getMany();

    let index_image = _.filter(push2_3, { type: 2 });
    let headlines = _.filter(push2_3, { type: 3 });

    for (let el of push1) {
      if (el.event_geo && el.event_geo.name === '申请截止') {
        el.event_geo.name = 'Deadline';
      }
      
      // 根据event_id在index_image中找到对应的数据，把找到数据的image字段加到el对象里面
      const matchedImage = index_image.find(img => img.event_id === el.event_id);
      if (matchedImage && matchedImage.image) {
        el.image = matchedImage.image;
      }
    }

    let index_image_level1 = _.filter(index_image, { level: 1 });
    let index_image_level2 = _.filter(index_image, { level: 2 });
    let index_image_level3 = _.filter(index_image, { level: 3 });

    headlines = _.orderBy(headlines, ['order'], ['desc']);

    let event_list = {
      events: push1,
      index_image: {
        level1: _.orderBy(index_image_level1, ['order'], ['desc']),
        level2: _.orderBy(index_image_level2, ['order'], ['desc']),
        level3: _.orderBy(index_image_level3, ['order'], ['desc']),
      },
      headlines,
    };

    await this.midwayCache.set(
      this.redisKey.eventList,
      JSON.stringify(event_list)
    );

    return event_list;
  }

  async majorCategory() {
    let items = {};

    const majors = await this.tblMajorCategoryDic.find({
      where: {},
    });

    for (const major of majors) {
      if (items[major.type]) {
        items[major.type].push(major);
      } else {
        let arr = [];
        arr.push(major);
        items[major.type] = arr;
      }
    }

    let output = [];

    for (const [key, value] of Object.entries(items)) {
      output.push({
        g: key,
        names: value,
      });
    }

    return output;
  }

  async schoolCreateAndUpdate(param: any) {
    const filePath = `${this.baseDir}/../upload/events/event-IMG/${param.directory}`;

    if (param?.directory?.length === 0 || !fs.existsSync(filePath)) {
      throw new Error('目录不存在，请联系管理员创建目录');
    }

    param.created_at = this.utils.today();
    param.updated_at = this.utils.today();

    const school = await this.tblSchoolDic.save(param);

    for (const obj of param?.majors) {
      await this.tblSchoolMajorDic.save({
        id: obj.id,
        school_id: school.id,
        fullname_chinese: obj.fullname_chinese,
        fullname_english: obj.fullname_english,
        short: obj.short,
        major_id: obj.major_id,
        school_start_time: 0,
        created_at: this.utils.today(),
      });
    }

    return school;
  }

  async schoolList(param: any) {
    const { page = 1, pageSize = 50, s } = param;
    const skip = (page - 1) * pageSize;

    const queryBuilder = this.tblSchoolDic
      .createQueryBuilder('school')
      .leftJoinAndSelect('school.majors', 'major')
      .orderBy('school.id', 'DESC')
      .addOrderBy('major.id', 'ASC')
      .skip(skip)
      .take(pageSize);

    if (s) {
      queryBuilder.where('school.keyword LIKE :keyword', { keyword: `%${s}%` });
    }

    return await queryBuilder.getManyAndCount();
  }

  async userDelete(param: any) {
    await this.tblEventUser.delete({ id: param.id });
  }

  async userCreateAndUpdate(param: any) {
    if (param?.password?.length) {
      param.password = this.utils.encodeDZString(param.password);
    }
    param.status = 1;

    await this.tblEventUser.save(param);
  }

  async userList(param: any) {
    const page = param.page || 1;
    const pageSize = param.pageSize || 30;

    const where: any = {};

    if (param.type) {
      where.type = param.type;
    }

    return await this.tblEventUser.findAndCount({
      select: ['id', 'username', 'nickname', 'type', 'status', 'created_at'],
      where: where,
      order: { id: 'DESC' },
      skip: (page - 1) * pageSize,
      take: pageSize,
    });
  }

  async changeOrder(param: any) {
    if (param.type === 1 || param.type === 3) {
      const ids = param.ids;
      let count = ids.length;

      for (const id of ids) {
        await this.tblEventRelease.update(
          { id: id },
          {
            order: count--,
          }
        );
      }
    } else {
      for (const row of param.data) {
        const ids = row.ids;
        let count = ids.length;

        for (const id of ids) {
          await this.tblEventRelease.update(
            { id: id },
            {
              level: row.level,
              order: count--,
            }
          );
        }
      }
    }

    await this.clearReleaseCache();
  }

  async push2ComingSoon(param: any) {
    const push2 = await this.tblEventRelease.find({
      where: {
        status: MoreThanOrEqual(0),
        type: 2,
      },
    });

    const level1 = _.filter(push2, { level: 1 });
    const level2 = _.filter(push2, { level: 2 });
    const level3 = _.filter(push2, { level: 3 });

    return {
      level1: _.orderBy(level1, ['order'], ['desc']),
      level2: _.orderBy(level2, ['order'], ['desc']),
      level3: _.orderBy(level3, ['order'], ['desc']),
    };
  }

  async push1Count(param: any) {
    return await this.tblSystemSettings.findOne({
      where: {
        name: 'push1_count',
      },
    });
  }

  async push1CountUpdate(param: any) {
    await this.tblSystemSettings.update(
      { name: 'push1_count' },
      {
        value: param.value,
      }
    );
  }

  async wwwHot(param: any) {
    return await this.tblSystemSettings.findOne({
      where: {
        name: 'www_hot',
      },
    });
  }

  async wwwHotUpdate(param: any) {
    await this.tblSystemSettings.update(
      { name: 'www_hot' },
      {
        value: param.value,
      }
    );
  }

  async push1WwwPosition(param: any) {
    return await this.tblSystemSettings.findOne({
      where: {
        name: 'push1_www_position',
      },
    });
  }

  async push1WwwPositionUpdate(param: any) {
    await this.tblSystemSettings.update(
      { name: 'push1_www_position' },
      {
        value: param.value,
      }
    );
  }

  async push1Add(param: any) {
    await this.tblEventRelease.save(param);
  }

  async releaseEditOne(param: any) {
    const obj = Object.assign({}, param);
    await this.tblEventRelease.update(param.id, obj);

    await this.clearReleaseCache();
  }

  async getReleaseOne(param) {
    return await this.tblEventRelease.findOne({
      relations: ['event_geo'],
      where: {
        id: param.id,
      },
    });
  }

  async releaseDelete(param) {
    const release = await this.tblEventRelease.findOneBy({ id: param.id });

    await this.tblEventRelease.delete({
      id: param.id,
    });

    await this.tblEvent.update(
      {
        id: release.event_id,
      },
      {
        [`push_${release.type}`]: 0,
      }
    );

    await this.clearReleaseCache();
  }

  async releaseList(param) {
    const page = param.page || 1;
    const pageSize = param.pageSize || 30;
    let status = param.status || 0;
    let type = param.type || 1;

    let where = {
      type,
      status,
    };

    let order: any =
      type == 1 ? { event_begin_date: 'ASC' } : { order: 'DESC' };

    if (status == -1) {
      order = { event_end_date: 'DESC' };
    }

    return await this.tblEventRelease.findAndCount({
      relations: ['event_geo'],
      where: where,
      order,
      skip: (page - 1) * pageSize,
      take: pageSize,
    });
  }

  async getGeo(param: any) {
    return await this.tblGeo.findOne({
      select: ['id', 'name'],
      where: {
        id: param.id,
      },
    });
  }

  async calendarDelete(param: any) {
    await this.tblEventCalendar.delete({ id: param.id });
  }

  async calendarList(param: any) {
    const page = param.page || 1;
    const pageSize = param.pageSize || 50;
    let status = param.status || 0;

    let where = {
      status,
      event_end_date: MoreThanOrEqual(this.utils.now()),
    };

    let order: any = { event_begin_date: 'ASC' };

    if (status > 0) {
      order = { event_begin_date: 'DESC' };
      delete where.event_end_date;
    } else if (status == -1) {
      order = { event_end_date: 'DESC' };
      where.status = -1;
      delete where.event_end_date;
    }

    return await this.tblEventCalendar.findAndCount({
      relations: ['event_school', 'event_location', 'event_geo'],
      where: where,
      order,
      skip: (page - 1) * pageSize,
      take: pageSize,
    });
  }

  async release1To3(param: any) {
    let where = {
      status: 0,
      event_id: param.eventId,
      type: param.type,
    };

    if (param.type == 1) {
      const locations = await this.tblEventLocation.find({
        where: {
          event_id: param.eventId,
          end_date: MoreThanOrEqual(this.utils.now()),
        },
      });

      const ers = await this.tblEventRelease.find({
        relations: ['event_geo'],
        where: where,
      });

      return {
        ers,
        locations,
      };
    } else {
      return await this.tblEventRelease.find({
        relations: ['event_geo'],
        where: where,
      });
    }
  }

  async releaseEdit(param: any) {
    if (param.type == 1) {
      for (const location of param.locations) {
        const push1 = await this.tblEventRelease.findOneBy({ id: location.id });
        if (location.id > 0 && push1) {
          await this.tblEventRelease.update(
            { id: location.id },
            {
              url1: param.url1,
              url2: param.url2,
              subject: location.subject,
              push_begin_date: location.push_begin_date,
              push_end_date:
                location.push_end_date || this.utils.afterNow99Years(),
              new_flag_date: location.new_flag_date,
              html: param.html == 1,
            }
          );
        } else {
          const loc = await this.tblEventLocation.findOneBy({
            id: location.lid,
          });
          location.country_id = loc.country_id;
          location.province_id = loc.province_id;
          location.city_id = loc.city_id;

          const obj: any = {};
          obj.type = param.type;
          obj.html = param.html;
          obj.eventId = param.eventId;
          obj.country_id = loc.country_id;
          obj.province_id = loc.province_id;
          obj.city_id = loc.city_id;
          obj.event_begin_date = location.event_begin_date;
          obj.event_end_date =
            location.event_end_date || this.utils.afterNow99Years();
          obj.push_begin_date = location.push_begin_date;
          obj.push_end_date =
            location.push_end_date || this.utils.afterNow99Years();
          obj.new_flag_date = location.new_flag_date;

          obj.url1 = param.url1;
          obj.url2 = param.url2;
          obj.subject = location.subject;
          obj.created_at = this.utils.now();

          obj.locations = [location];

          await this.releaseCreate(obj);
        }
      }
    } else if (param.type == 2) {
      await this.tblEventRelease.update(
        { id: param.id },
        {
          subject: param.subject,
          push_begin_date: param.push_begin_date,
          push_end_date: param.push_end_date || this.utils.afterNow99Years(),
          url1: param.url1,
          image: param.image,
        }
      );
    } else if (param.type == 3) {
      await this.tblEventRelease.update(
        { id: param.id },
        {
          subject: param.subject,
          push_begin_date: param.push_begin_date,
          push_end_date: param.push_end_date || this.utils.afterNow99Years(),
          url1: param.url1,
          color: param.color,
        }
      );
    }

    await this.clearReleaseCache();
  }

  async releaseCreate(param: any) {
    let where: any = { type: param.type };

    if (parseInt(param.type, 10) === 2) {
      where.level = param.level || 1;
    }

    const max: any =
      (await this.tblEventRelease.findOne({
        where: where,
        order: { order: 'DESC' },
      })) || {};

    let order = max.order || 0;
    ++order;

    if (parseInt(param.type, 10) === 1) {
      if (param.html == 1) {
        await this.tblEventRelease.save({
          event_id: param.eventId || 0,
          subject: param.subject,
          event_begin_date: param.event_begin_date,
          event_end_date: param.event_end_date || this.utils.afterNow99Years(),
          push_begin_date: param.push_begin_date,
          push_end_date: param.push_end_date || this.utils.afterNow99Years(),
          new_flag_date: param.new_flag_date,
          country_id: param.country_id || 0,
          province_id: param.province_id || 0,
          city_id: param.city_id || 0,
          type: 1,
          html: true,
          order,
          created_at: new Date(),
        });
      } else {
        for (const location of param.locations) {
          const obj: any = {};
          obj.event_id = param.eventId;
          obj.event_begin_date = location.event_begin_date;
          obj.event_end_date =
            location.event_end_date || this.utils.afterNow99Years();
          obj.push_begin_date = location.push_begin_date;
          obj.push_end_date =
            location.push_end_date || this.utils.afterNow99Years();
          obj.new_flag_date = location.new_flag_date;

          obj.country_id = location.country_id;
          obj.province_id = location.province_id;
          obj.location_id =
            parseInt(location.city_id, 10) || location.province_id;
          obj.lid = location.lid;
          obj.subject = location.subject;
          obj.order = order;
          obj.type = param.type;
          obj.html = false;
          obj.url1 = param.url1;
          obj.url2 = param.url2;
          obj.created_at = new Date();

          await this.tblEventRelease.save(obj);

          ++order;
        }
      }
    } else {
      await this.tblEventRelease.save({
        ...param,
        event_id: param.eventId || 0,
        created_at: new Date(),
      });
    }

    if (param.html != 1) {
      await this.tblEvent.update(
        {
          id: param.eventId,
        },
        {
          [`push_${param.type}`]: true,
        }
      );
    }

    await this.clearReleaseCache();
  }

  async clearReleaseCache() {
    await this.midwayCache.del(this.redisKey.eventList);
  }

  async calendarEdit(param: any) {
    for (const location of param.locations) {
      const calendar = await this.tblEventCalendar.findOneBy({
        id: location.id,
      });

      if (location.id > 0 && calendar) {
        await this.tblEventCalendar.update(location.id, {
          subject: location.subject,
          push_begin_date: location.push_begin_date,
          push_end_date: location.push_end_date || this.utils.afterNow99Years(),
          new_flag_date: location.new_flag_date,
          position: location.position,
          multi_day: location.multi_day,
          image: location.image,
          type: param.type,
          url1: param.www_url,
          url2: param.forum_url,
          url3: param.apply_url,
        });
      } else {
        const obj: any = {
          ...param,
          url1: param.www_url,
          url2: param.forum_url,
          url3: param.apply_url,
        };

        obj.locations = [location];

        this.calendar(obj);
      }
    }
  }

  async calendarEditOne(param: any) {
    await this.tblEventCalendar.update(param.id, {
      url1: param.url1,
      url2: param.url2,
      subject: param.subject,
      push_begin_date: param.push_begin_date,
      push_end_date: param.push_end_date || this.utils.afterNow99Years(),
      new_flag_date: param.new_flag_date,
      position: param.position,
      image: param.image,
    });
  }

  async getCalendar(id) {
    const calendar = await this.tblEventCalendar.findOneBy(id);
    // @ts-ignore
    calendar.event_type = (
      await this.tblEventType.find({
        select: ['event_type_id'],
        where: {
          event_id: calendar.event_id,
          lid: calendar.lid.toString(),
        },
      })
    ).map(row => row.event_type_id);
    // @ts-ignore
    calendar.school_major = (
      await this.tblEventSchoolMajor.find({
        select: ['school_major_id'],
        where: {
          event_id: calendar.event_id,
          lid: calendar.lid.toString(),
        },
      })
    ).map(row => row.school_major_id);
    // @ts-ignore
    calendar.geo = await this.tblGeo.findOneBy({
      id: calendar.location_id,
    });

    return calendar;
  }

  async calendarInfo(param: any) {
    const eventId = param.eventId;

    const locations = await this.tblEventLocation.find({
      where: {
        event_id: eventId,
        end_date: MoreThanOrEqual(this.utils.now()),
      },
    });

    for (let location of locations) {
      // @ts-ignore
      location.event_school_major = (
        await this.tblEventSchoolMajor.find({
          select: ['school_major_id'],
          where: {
            event_id: eventId,
            lid: location.id,
          },
        })
      ).map(row => {
        return row.school_major_id;
      });
      // @ts-ignore
      location.event_major = (
        await this.tblEventMajor.find({
          select: ['major_id'],
          where: {
            event_id: eventId,
            lid: location.id,
          },
        })
      ).map(row => {
        return row.major_id;
      });
      // @ts-ignore
      location.event_type = (
        await this.tblEventType.find({
          select: ['event_type_id'],
          where: {
            event_id: eventId,
            lid: location.id,
          },
        })
      ).map(row => {
        return row.event_type_id;
      });
    }

    const ecs = await this.tblEventCalendar.find({
      where: {
        status: 0,
        event_id: eventId,
        event_end_date: MoreThanOrEqual(this.utils.now()),
      },
      relations: ['event_school', 'event_location', 'event_geo'],
    });

    for (const ec of ecs) {
      // @ts-ignore
      ec.event_type = await this.tblEventType.find({
        where: {
          lid: ec.lid.toString(),
        },
      });
      // @ts-ignore
      ec.event_major = await this.tblEventMajor.find({
        where: {
          lid: ec.lid.toString(),
        },
      });
      // @ts-ignore
      ec.school_major = await this.tblEventSchoolMajor.find({
        where: {
          lid: ec.lid.toString(),
        },
      });
    }

    return {
      ecs,
      locations,
    };
  }

  async calendar(param: any) {
    for (const location of param.locations) {
      const obj: any = {};

      obj.event_id = param.eventId;
      obj.type = param.type;
      obj.event_begin_date = location.event_begin_date;
      obj.event_end_date =
        location.event_end_date || this.utils.afterNow99Years();
      obj.push_begin_date = location.push_begin_date;
      obj.push_end_date =
        location.push_end_date || this.utils.afterNow99Years();
      obj.new_flag_date = location.new_flag_date;

      obj.school_id = param.school_id;
      obj.location_id =
        location.location_id ||
        parseInt(location.city_id, 10) ||
        location.province_id;
      obj.lid = location.lid || location.id;
      obj.subject = location.subject;
      obj.image = location.image;
      obj.position = location.position;
      obj.multi_day = location.multi_day;
      obj.url1 = param.www_url;
      obj.url2 = param.forum_url;
      obj.url3 = param.apply_url;
      obj.created_at = new Date();

      await this.tblEventCalendar.save(obj);
    }

    await this.tblEvent.update(
      {
        id: param.eventId,
      },
      {
        push_0: true,
      }
    );
  }

  async core(param: any) {
    const { eventId } = param;

    const event = await this.tblEvent.findOne({
      where: { id: eventId },
      relations: ['event_school', 'event_location'],
    });

    for (const location of event.event_location) {
      // @ts-ignore
      location.school_major = (
        await this.tblEventSchoolMajor.find({
          select: ['school_major_id'],
          where: {
            event_id: event.id,
            lid: location.id,
          },
        })
      ).map(row => {
        return row.school_major_id;
      });
      // @ts-ignore
      location.event_type = (
        await this.tblEventType.find({
          select: ['event_type_id'],
          where: {
            event_id: event.id,
            lid: location.id,
          },
        })
      ).map(row => {
        return row.event_type_id;
      });
      // @ts-ignore
      location.event_geo = await this.tblGeo.findOne({
        select: ['id', 'name'],
        where: {
          id:
            parseInt(location.city_id, 10) ||
            parseInt(location.province_id, 10),
        },
      });
    }

    return event;
  }

  async remove(param: any) {
    const eventId = param.eventId;
    const event = await this.tblEvent.findOne({
      where: {
        id: eventId,
      },
    });

    await this.tblEvent.update(
      { id: eventId },
      {
        status: -1,
        push_0: false,
        push_1: false,
        push_2: false,
        push_3: false,
        www_url: 0,
        forum_url: 0,
        highlight: 0,
        digest: 0,
        stick: 0,
      }
    );

    await this.tblEventCalendar.update(
      { event_id: eventId },
      {
        status: -1,
      }
    );

    await this.tblEventRelease.update(
      { event_id: eventId },
      {
        status: -1,
      }
    );

    if (event.www_url) {
      await this.iwmsNews.delete({
        articleid: event.www_url,
      });
    }

    if (event.forum_url) {
      const user = await this.tblEventUser.findOneBy({
        id: '2',
      });

      const body = {
        username: user.username,
        password: this.utils.decodeDZString(user.password),
        tid: event.forum_url,
        fid: event.forum_url_fid,
      };

      await this.commonForumService.deleteThread(body);
    }
  }

  async edit(param: any) {
    const content = await this.replaceLinks(param.content, param.eventId);

    await this.tblEvent.update(param.eventId, {
      subject: param.subject,
      content,
      school_id: param.school_id,
    });

    const event = await this.tblEvent.findOneBy({ id: param.eventId });

    if (event.www_url) {
      await this.portalArticleEntity.update(
        {
          id: event.www_url,
        },
        {
          title: param.subject,
          content: this.utils.replaceLinksForPush(content, 'p'),
        }
      );
      
      await this.es.client.update({
        index: 'portal_articles',
        id: event.www_url.toString(),
        body: {
          doc: {
            title: param.subject,
            content: this.utils.replaceLinksForPush(content, 'p')?.replace(/<[^>]+>/g, ''),
          }
        }
      });
    }

    if (event.forum_url) {
      const chasedream = await this.tblEventUser.findOne({
        where: {
          username: '<EMAIL>',
        },
      });

      await this.commonForumService.updateThread({
        username: chasedream.username,
        password: this.utils.decodeDZString(chasedream.password),
        tid: event.forum_url,
        fid: event.forum_url_fid,
        pid: event.forum_url_pid,
        subject: param.subject,
        content: this.utils.replaceLinksForPush(content, 'f'),
        typeid: event.forum_url_typeid,
        htmlon: 1,
      });
    }

    if (event.apply_url) {
      await this.tblEventApplyCalendar.update(
        { id: event.apply_url.toString() },
        {
          content: this.utils.replaceLinksForPush(content, 'c'),
        }
      );
    }

    if (event.iese_url) {
      await this.tblEventIESE.update(
        { event_id: param.eventId },
        {
          subject: param.subject,
          content: this.utils.replaceLinksForPush(content, 'iesecn'),
        }
      );
    }

    await this.tblEventCalendar.update(
      { event_id: param.eventId },
      {
        school_id: param.school_id,
      }
    );

    for (const location of param.locations) {
      if (location.id > 0) {
        await this.locationUpdate(location);

        if(event.push_0) await this.updatePush0Date(location);
        if(event.push_1) await this.updatePush1Date(location);
      } else {
        await this.locationCreate(location, param.eventId);
      }
    }

    await this.clearReleaseCache();
  }

  async updatePush0Date(location: any) {
    try {
      await this.tblEventCalendar.update(
        { event_id: location.event_id, lid: location.id },
        {
          event_begin_date: location.begin_date,
          event_end_date: location.end_date,
        }
      );
    } catch (err) {
      this.logger.error(`updatePush0Date: ${err.message}`);
    }
  }

  async updatePush1Date(location: any) {
    try {
      await this.tblEventRelease.update(
        { event_id: location.event_id, lid: location.id, type: 1 },
        {
          event_begin_date: location.begin_date,
          event_end_date: location.end_date,
        }
      );
    } catch (err) {
      this.logger.error(`updatePush1Date: ${err.message}`);
    }
  }

  async locationCreate(location, eventId) {
    const el = await this.tblEventLocation.save({
      event_id: eventId,
      country_id: location.country_id || 0,
      province_id: location.province_id || 0,
      city_id: location.city_id || 0,
      begin_date: location.begin_date,
      end_date: location.end_date || this.utils.afterNow99Years(),
      app_join_event: location.app_join_event,
      created_at: new Date(),
    });

    for (let event_type_id of location.event_type) {
      const et = await this.tblEventTypeDict.findOne({
        where: {
          id: event_type_id,
        },
      });
      await this.tblEventType.save({
        event_id: eventId,
        lid: el.id,
        event_type_id,
        event_type_name: et.name,
      });
    }

    for (let school_major_id of location.school_major) {
      await this.tblEventSchoolMajor.save({
        event_id: eventId,
        lid: el.id,
        school_major_id,
      });
    }

    const major = (
      await this.tblSchoolMajorDic.find({
        where: {
          id: In(location.school_major),
        },
      })
    ).map(row => {
      return row.major_id;
    });

    for (let major_id of major) {
      const em = await this.tblMajorCategoryDic.findOne({
        where: {
          id: major_id,
        },
      });
      await this.tblEventMajor.save({
        event_id: eventId,
        lid: el.id,
        major_id,
        major_name: em.display_name,
      });
    }

    await this.tblEvent.update(
      {
        id: eventId,
      },
      {
        status: 0,
      }
    );
  }

  async locationUpdate(location: any) {
    await this.tblEventLocation.update(
      { id: location.id },
      {
        country_id: location.country_id || 0,
        province_id: location.province_id || 0,
        city_id: location.city_id || 0,
        begin_date: location.begin_date,
        end_date: location.end_date || this.utils.afterNow99Years(),
        app_join_event: location.app_join_event,
      }
    );

    await this.tblEventCalendar.update(
      { lid: location.id },
      {
        location_id: location.city_id || location.province_id,
      }
    );

    const ets = (
      await this.tblEventType.find({
        select: ['event_type_id'],
        where: {
          event_id: location.event_id,
          lid: location.id,
        },
      })
    ).map(row => {
      return row.event_type_id;
    });

    let current_et = new Set(ets);
    let new_et = new Set(location.event_type);
    let all_et = new Set([...current_et, ...new_et]);
    // @ts-ignore
    let need_add: any = new Set([...all_et].filter(x => !current_et.has(x)));
    let need_del: any = new Set([...all_et].filter(x => !new_et.has(x)));

    if (need_add.size) {
      need_add = Array.from(need_add);

      for (const event_type_id of need_add) {
        const et = await this.tblEventTypeDict.findOne({
          where: {
            id: event_type_id,
          },
        });
        await this.tblEventType.save({
          event_id: location.event_id,
          lid: location.id,
          event_type_id,
          event_type_name: et.name,
        });
      }
    }

    if (need_del.size) {
      need_del = Array.from(need_del);

      for (const event_type_id of need_del) {
        await this.tblEventType.delete({
          event_id: location.event_id,
          lid: location.id,
          event_type_id,
        });
      }
    }

    const ems = (
      await this.tblEventMajor.find({
        select: ['major_id'],
        where: {
          event_id: location.event_id,
          lid: location.id,
        },
      })
    ).map(row => {
      return row.major_id;
    });

    const major = (
      await this.tblSchoolMajorDic.find({
        where: {
          id: In(location.school_major),
        },
      })
    ).map(row => {
      return row.major_id;
    });

    let current_em = new Set(ems);
    let new_em = new Set(major);
    let all_em = new Set([...current_em, ...new_em]);
    // @ts-ignore
    need_add = new Set([...all_em].filter(x => !current_em.has(x)));
    need_del = new Set([...all_em].filter(x => !new_em.has(x)));

    if (need_add.size) {
      need_add = Array.from(need_add);

      for (const major_id of need_add) {
        const em = await this.tblMajorCategoryDic.findOne({
          where: {
            id: major_id,
          },
        });
        await this.tblEventMajor.save({
          event_id: location.event_id,
          lid: location.id,
          major_id,
          major_name: em.display_name,
        });
      }
    }

    if (need_del.size) {
      need_del = Array.from(need_del);

      for (const major_id of need_del) {
        await this.tblEventMajor.delete({
          event_id: location.event_id,
          lid: location.id,
          major_id,
        });
      }
    }

    const esm = (
      await this.tblEventSchoolMajor.find({
        select: ['school_major_id'],
        where: {
          event_id: location.event_id,
          lid: location.id,
        },
      })
    ).map(row => {
      return row.school_major_id;
    });

    let current_esm = new Set(esm);
    let new_esm = new Set(location.school_major);
    let all_esm = new Set([...current_esm, ...new_esm]);
    // @ts-ignore
    need_add = new Set([...all_esm].filter(x => !current_esm.has(x)));
    need_del = new Set([...all_esm].filter(x => !new_esm.has(x)));

    if (need_add.size) {
      need_add = Array.from(need_add);

      for (const school_major_id of need_add) {
        await this.tblEventSchoolMajor.save({
          event_id: location.event_id,
          lid: location.id,
          school_major_id,
        });
      }
    }

    if (need_del.size) {
      need_del = Array.from(need_del);

      for (const school_major_id of need_del) {
        await this.tblEventSchoolMajor.delete({
          event_id: location.event_id,
          lid: location.id,
          school_major_id,
        });
      }
    }

    await this.tblEvent.update(
      {
        id: location.event_id,
      },
      {
        status: 0,
      }
    );
  }

  async pushLocationDelete(param) {
    if (param.type == 0) {
      await this.tblEventCalendar.delete({
        id: param.id,
      });
    } else if (param.type == 1) {
      await this.tblEventRelease.delete({
        id: param.id,
      });
    }
  }

  async coreLocationDelete(param) {
    await this.tblEventLocation.delete({
      id: param.id,
    });

    await this.tblEventType.delete({
      lid: param.id,
    });

    await this.tblEventMajor.delete({
      lid: param.id,
    });
  }

  async replaceLinks(html, eventId) {
    const links = [];
    const regex =
      /<a\s+([^>]*?)href="(?!https:\/\/go\.chasedream\.com|mailto:)([^"]*?)"(.*?)>/g;
    let match;

    while ((match = regex.exec(html)) !== null) {
      links.push({
        index: match.index,
        length: match[0].length,
        prefix: match[1],
        href: match[2],
        suffix: match[3],
      });
    }

    links.sort((a, b) => b.index - a.index);

    for (let link of links) {
      let exist = true;
      let shortUrl = '';

      do {
        shortUrl = this.utils.shortId();

        exist = await this.tblEventExternalLink.exists({
          where: { shortUrl },
        });
      } while (exist);

      const existLink = await this.tblEventExternalLink.findOne({
        where: {
          event_id: eventId,
          targetUrl: link.href.replace(/\n/g, ''),
        },
      });

      if (!existLink) {
        await this.tblEventExternalLink.save({
          event_id: eventId,
          targetUrl: link.href.replace(/\n/g, ''),
          shortUrl,
          created_at: this.utils.now(),
        });
      }

      const newLink = `<a ${link.prefix}href="https://go.chasedream.com/e/${
        existLink ? existLink.shortUrl : shortUrl
      }"${link.suffix}>`;
      html =
        html.substring(0, link.index) +
        newLink +
        html.substring(link.index + link.length);
    }

    return html;
  }

  async create(param: any) {
    const { subject, school_id, content, locations } = param;

    const event = await this.tblEvent.save({
      subject,
      school_id,
      content,
      created_at: new Date(),
    });

    const html = await this.replaceLinks(content, event.id);
    await this.tblEvent.update(
      {
        id: event.id,
      },
      {
        content: html,
      }
    );

    for (const location of locations) {
      const loc = {
        event_id: event.id,
        country_id: location.country_id || 0,
        province_id: location.province_id || 0,
        city_id: location.city_id || 0,
        app_join_event: location.app_join_event,
        begin_date: location.begin_date || 0,
        end_date: location.end_date || this.utils.afterNow99Years(),
        created_at: new Date(),
      };

      const el = await this.tblEventLocation.save(loc);

      for (const school_major_id of location.school_major) {
        await this.tblEventSchoolMajor.save({
          event_id: event.id,
          lid: el.id,
          school_major_id: school_major_id,
        });

        const smd = await this.tblSchoolMajorDic.findOneBy({
          id: school_major_id,
        });

        const mcd = await this.tblMajorCategoryDic.findOneBy({
          id: smd.major_id,
        });

        await this.tblEventMajor.save({
          event_id: event.id,
          lid: el.id,
          major_id: smd.major_id,
          major_name: mcd.display_name,
          created_at: new Date(),
        });
      }

      for (const event_type_id of location.event_type) {
        const et = await this.tblEventTypeDict.findOneBy({
          id: event_type_id,
        });

        await this.tblEventType.save({
          event_id: event.id,
          lid: el.id,
          event_type_id: event_type_id,
          event_type_name: et.name,
          created_at: new Date(),
        });
      }
    }

    return event;
  }

  async imageDelete(param: any) {
    await this.tblUpload.delete({ id: param.id });
  }

  async imageDigest(param: any) {
    await this.tblUpload.update(param.id, {
      school_id: param.school_id,
      digest: param.digest,
    });
  }

  async materialDigest(param: any) {
    const page = param.page || 1;
    const pageSize = param.pageSize || 24;

    const where: any = {
      school_id: param.school_id,
    };

    where.digest = param.digest ? 1 : 0;

    return await this.tblUpload.findAndCount({
      select: ['id', 'school_id', 'digest', 'fullpath', 'order', 'created_at'],
      where,
      order: {
        order: 'DESC',
        id: 'DESC',
      },
      skip: (page - 1) * pageSize,
      take: pageSize,
    });
  }

  async major(param: any) {
    return await this.tblSchoolMajorDic.find({
      where: {
        school_id: param.school_id,
      },
    });
  }

  async types(param: any) {
    return await this.tblEventTypeDict.find();
  }

  async geo(param: any) {
    const where = param.parent_id
      ? { parent_id: param.parent_id }
      : { parent_id: 0 };

    return await this.tblGeo.find({
      select: ['id', 'name'],
      where: where,
      order: {
        order: 'DESC',
      },
    });
  }

  async orgs(param: any) {
    return await this.tblSchoolDic.find();
  }

  async urlPage(param: any) {
    const { subject, page, pageSize } = param;

    let status = param.status || 0;

    const queryBuilder = this.tblEvent
      .createQueryBuilder('event')
      .innerJoinAndSelect(
        'event.event_location',
        'eventLocation',
        status == -1
          ? 'eventLocation.end_date <= :now'
          : 'eventLocation.end_date >= :now',
        { now: this.utils.now() }
      )
      .leftJoinAndSelect('event.event_school', 'eventSchool')
      .where('event.status = 0');

    if (subject) {
      queryBuilder.andWhere('event.subject LIKE :subject', {
        subject: `%${subject}%`,
      });
    }

    queryBuilder.orderBy('event.id', 'DESC');

    const currentPage = page || 1;
    const size = pageSize || 50;
    queryBuilder.skip((currentPage - 1) * size).take(size);

    const [list, total] = await queryBuilder.getManyAndCount();

    return {
      list,
      total,
    };
  }
}
