import { Inject, Provide } from '@midwayjs/decorator';
import { BaseService } from '@cool-midway/core';
import { InjectEntityModel } from '@midwayjs/typeorm';
import { Repository } from 'typeorm';
import { CommonHotspotEntity } from '../../entity/common/hotspot';
import { Utils } from '../../../../comm/utils';
import { CommonHotspotQueueEntity } from '../../entity/common/hotspotQueue';
import * as _ from 'lodash';

/**
 * Hotspot
 */
@Provide()
export class CommonHotspotService extends BaseService {
  @InjectEntityModel(CommonHotspotEntity)
  commonHotspotEntity: Repository<CommonHotspotEntity>;

  @InjectEntityModel(CommonHotspotQueueEntity)
  commonHotspotQueueEntity: Repository<CommonHotspotQueueEntity>;

  @Inject()
  utils: Utils;

  async info() {
    const currentTime = this.utils.now();

    const res = await this.commonHotspotQueueEntity
      .createQueryBuilder('queue')
      .where('queue.begin <= :currentTime', {
        currentTime,
      })
      .orderBy('queue.id', 'ASC')
      .getOne();

    if (res) {
      await this.commonHotspotQueueEntity.remove(res);

      delete res.id;
      delete res.pid;
      delete res.createTime;
      delete res.updateTime;
    }

    return res;
  }

  async hotspotCreate(param: any) {
    await this.commonHotspotEntity.save(param);
  }

  async hotSpotPage(param: any) {
    const { id, title, targetUrl } = param;

    const sql = `
        SELECT * FROM common_hotspot 
        WHERE 1 = 1 
            ${this.setSql(!_.isEmpty(id), 'and id=?', id)}
            ${this.setSql(!_.isEmpty(title), 'and title=?', title)}
            ${this.setSql(
              !_.isEmpty(targetUrl),
              'and targetUrl like ?',
              `%${targetUrl}%`
            )}
            ORDER BY CASE 
                WHEN status = 0 THEN 0 
                WHEN status = -1 THEN 1
                ELSE 2 
            END, 
            id DESC`;
    return this.sqlRenderPage(sql, param, false);
  }

  async hotspotAdd(param: any) {
    return await this.commonHotspotEntity.save({
      title: param.title,
      targetUrl: param.targetUrl,
      begin: param.begin || 0,
      end: param.end || 0,
      showTimeMin: param.showTimeMin || 0,
      showTimeMax: param.showTimeMax || 0,
      showIntervalMin: param.showIntervalMin || 0,
      showIntervalMax: param.showIntervalMax || 0,
      referer: param.referer,
      type: param.type,
    });
  }

  async hotspotUpdate(param: any) {
    param.begin = param.begin || 0;
    param.end = param.end || 0;

    await this.commonHotspotEntity.update(param.id, {
      ...param,
    });
  }

  async hotspotDelete(param: any) {
    await this.commonHotspotEntity.delete({ id: param.id });
  }
}
