import { Provide } from '@midwayjs/decorator';
import { BaseService } from '@cool-midway/core';
import { InjectEntityModel } from '@midwayjs/typeorm';
import { Repository } from 'typeorm';
import { CcCommonSearchLog } from '../../entity/forum/CcCommonSearchLog';
import * as _ from 'lodash';

/**
 * 论坛搜索日志
 */
@Provide()
export class CommonSearchLogService extends BaseService {
  @InjectEntityModel(CcCommonSearchLog, 'forum')
  ccCommonSearchLog: Repository<CcCommonSearchLog>;

  async list(param) {
    const { id, username, content, ip } = param;

    const sql = `
        SELECT * FROM cc_common_search_log WHERE 1 = 1 
            ${this.setSql(!_.isEmpty(id), 'and id=?', id)}
            ${this.setSql(!_.isEmpty(username), 'and username=?', username)}
            ${this.setSql(!_.isEmpty(content), 'and content like ?', content)}
            ${this.setSql(!_.isEmpty(ip), 'and ip=?', ip)}
            ORDER BY id DESC`;
    return this.sqlRenderPage(sql, param, false, 'forum');
  }
}
