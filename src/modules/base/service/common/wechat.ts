import {
  Inject,
  Provide,
  Logger,
  Config,
  Task,
  FORMAT,
} from '@midwayjs/decorator';
import { InjectEntityModel } from '@midwayjs/typeorm';
import { Like, MoreThan, LessThan, Repository } from 'typeorm';
import { ILogger } from '@midwayjs/logger';
import { Context } from '@midwayjs/koa';
import * as _ from 'lodash';
import { promises as fsp } from 'fs';
import { WechatMessageEntity } from '../../entity/wx/wechatMessage';
import { Utils } from '../../../../comm/utils';
import { Pinji } from '../../entity/chiji/pinji';
import { WechatGroupEntity } from '../../entity/wx/wechatGroup';
import OpenAI from 'openai';
import { BaseService } from '@cool-midway/core';
const fs = require('fs');

/**
 * Wechat
 */
@Provide()
export class WechatService extends BaseService {
  @Inject()
  ctx: Context;

  @InjectEntityModel(WechatMessageEntity)
  wechatMessageEntity: Repository<WechatMessageEntity>;

  @InjectEntityModel(WechatGroupEntity)
  wechatGroupEntity: Repository<WechatGroupEntity>;

  @InjectEntityModel(Pinji, 'chiji')
  pinji: Repository<Pinji>;

  @Inject()
  baseDir;

  @Inject()
  utils: Utils;

  @Logger()
  logger: ILogger;

  @Config('chatgpt')
  chatgpt;

  @Config('wechaty')
  wechaty;

  async message(param) {
    const room = await global.wechatBot.Room.load(param.group.roomId);
    await room.say(param.text);
  }

  async updateGroup(param) {
    await this.wechatGroupEntity.update(param.id, {
      topic: param.topic,
      digest: param.digest ? 1 : 0,
    });
  }

  async createAssistant() {
    const openai = new OpenAI({
      apiKey: this.chatgpt.apiKey,
      baseURL: this.chatgpt.apiBaseUrl,
      timeout: this.chatgpt.timeoutMs,
      maxRetries: 5,
    });

    await openai.beta.assistants.create({
      name: '考题判断助手',
      instructions: `
        你现在是一个考题判断AI

        #约束
        - 只返回JSON数据
        - 返回的数据的条数必须和给你的保持相同
        
        #目标
        - 根据上下文判断每条对话是不是考试题
        - 如果你收到了文件，请直接判断文件是不是考试题，忽略给你的文字部分
        - 必须返回每一条对应的结果，例如: [true, false, false]
        
        #判断标准
        - 如果少于20个字不是考题
        - 如果有疑问语气不是考题`,
      model: 'gpt-4o',
    });
  }

  @Task({
    repeat: { cron: FORMAT.CRONTAB.EVERY_MINUTE },
    removeOnComplete: true,
  })
  async messageAssistant() {
    const messages = await this.wechatMessageEntity.find({
      where: {
        type: 7,
        status: 0,
      },
      take: 10,
    });

    if (messages.length === 0 || !this.wechaty.enable) return;

    const openai = new OpenAI({
      apiKey: this.chatgpt.apiKey,
      baseURL: this.chatgpt.apiBaseUrl,
      timeout: this.chatgpt.timeoutMs,
      maxRetries: 5,
    });

    const content = messages.map(row => {
      return {
        content: row.text.replace(/[^」]*」[^「]*\n-?/g, ''),
      };
    });

    const thread = await openai.beta.threads.create();

    try {
      await openai.beta.threads.messages.create(thread.id, {
        role: 'user',
        content: JSON.stringify(content),
      });

      const run = await openai.beta.threads.runs.create(thread.id, {
        assistant_id: this.chatgpt.assistantId,
      });

      let runStatus = await openai.beta.threads.runs.retrieve(
        thread.id,
        run.id
      );
      let attemptCount = 0;
      let waitTime = 2000;

      while (runStatus.status !== 'completed' && attemptCount < 5) {
        await this.utils.sleep(waitTime);

        runStatus = await openai.beta.threads.runs.retrieve(thread.id, run.id);
        attemptCount++;
      }

      if (runStatus.status !== 'completed')
        throw new Error(`assistant api run error: ${runStatus.status}`);
      const msg = await openai.beta.threads.messages.list(thread.id);

      const lastMessageForRun = msg.data
        .filter(
          message => message.run_id === run.id && message.role === 'assistant'
        )
        .pop();

      if (!lastMessageForRun) return;
      // @ts-ignore
      const json = JSON.parse(lastMessageForRun.content[0].text.value);

      if (json.length !== messages.length)
        throw new Error(
          `assistant api json not equals: ${json.length}, ${messages.length}`
        );

      for (let i = 0; i < messages.length; i++) {
        messages[i].exam = json[i];
      }

      for (const message of messages) {
        await this.wechatMessageEntity.update(message.id, {
          status: 1,
          exam: message.exam,
        });
      }
    } catch (err) {
      for (const message of messages) {
        await this.wechatMessageEntity.update(message.id, {
          status: -1,
        });
      }

      this.logger.error(err.message);
    } finally {
      await openai.beta.threads.del(thread.id);
    }
  }

  // @Task({
  //   repeat: { cron: FORMAT.CRONTAB.EVERY_MINUTE },
  //   removeOnComplete: true,
  // })
  async attachAssistant() {
    const openai = new OpenAI({
      apiKey: this.chatgpt.apiKey,
      baseURL: this.chatgpt.apiBaseUrl,
      timeout: this.chatgpt.timeoutMs,
      maxRetries: 5,
    });

    const message = await this.wechatMessageEntity.findOne({
      where: {
        type: 1,
        status: 0,
      },
    });

    if (!message) return;

    const file = await openai.files.create({
      file: fs.createReadStream(`${this.baseDir}/..${message.attachment}`),
      purpose: 'assistants',
    });

    const thread = await openai.beta.threads.create();

    try {
      await openai.beta.threads.messages.create(thread.id, {
        role: 'user',
        content: '',
        // @ts-ignore
        file_ids: [file.id],
      });

      const run = await openai.beta.threads.runs.create(thread.id, {
        assistant_id: this.chatgpt.assistantId,
      });

      let runStatus = await openai.beta.threads.runs.retrieve(
        thread.id,
        run.id
      );
      let attemptCount = 0;
      let waitTime = 2000;

      while (runStatus.status !== 'completed' && attemptCount < 5) {
        await this.utils.sleep(waitTime);

        runStatus = await openai.beta.threads.runs.retrieve(thread.id, run.id);
        attemptCount++;
      }

      if (runStatus.status !== 'completed')
        throw new Error(`assistant api run error: ${runStatus.status}`);
      const msg = await openai.beta.threads.messages.list(thread.id);

      const lastMessageForRun = msg.data
        .filter(
          message => message.run_id === run.id && message.role === 'assistant'
        )
        .pop();

      if (!lastMessageForRun) return;
      // @ts-ignore
      const json = JSON.parse(lastMessageForRun.content[0].text.value);

      await this.wechatMessageEntity.update(message.id, {
        status: 1,
        exam: json[0],
      });
    } catch (err) {
      await this.wechatMessageEntity.update(message.id, {
        status: -1,
      });

      this.logger.error(err.message);
    } finally {
      await openai.beta.threads.del(thread.id);
      await openai.files.del(file.id);
    }
  }

  async groupList(param) {
    const where: any = {};

    if (param.topic) {
      where.topic = Like(`%${param.topic}%`);
    }

    let group = await this.wechatGroupEntity.find({
      where,
      order: {
        digest: 'DESC',
        updateTime: 'DESC',
      },
    });

    return {
      group,
      wechatInfo: global.wechatInfo,
    };
  }

  async messages(param) {
    const where: any = {
      groupId: param.groupId,
    };

    if (param.type === 1) {
      where.id = MoreThan(param.id);
    } else if (param.type === 2) {
      where.id = LessThan(param.id);
    }

    return await this.wechatMessageEntity.find({
      where,
      take: 50,
      order: {
        id: 'DESC',
      },
    });
  }

  async initPinjiData() {
    const pinjis = await this.pinji.find({
      select: ['id', 'subject', 'content'],
    });

    const filePath = `${this.baseDir}/pinji.txt`;

    await fsp.writeFile(filePath, JSON.stringify(pinjis, null, 2));
  }
}
